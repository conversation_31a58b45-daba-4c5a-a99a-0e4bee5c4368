import numpy as np
import open3d as o3d
import cv2

def depth_to_pointcloud(depth_map, color_image, camera_intrinsics):
    """
    将深度图和彩色图转换为点云
    
    Args:
        depth_map: 深度图 (H, W)
        color_image: 彩色图 (H, W, 3)
        camera_intrinsics: 相机内参矩阵 (3, 3)
    
    Returns:
        pointcloud: 点云对象
    """
    # 获取图像尺寸
    height, width = depth_map.shape
    
    # 创建像素坐标网格
    x, y = np.meshgrid(np.arange(width), np.arange(height))
    
    # 将像素坐标转换为归一化坐标
    x_normalized = (x - camera_intrinsics[0, 2]) / camera_intrinsics[0, 0]
    y_normalized = (y - camera_intrinsics[1, 2]) / camera_intrinsics[1, 1]
    
    # 计算3D坐标
    z = depth_map
    x_3d = x_normalized * z
    y_3d = y_normalized * z
    
    # 组合为点云坐标
    points = np.stack([x_3d, y_3d, z], axis=-1)
    
    # 过滤无效深度点
    valid_mask = depth_map > 0
    valid_points = points[valid_mask]
    valid_colors = color_image[valid_mask]
    
    # 创建Open3D点云对象
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(valid_points)
    pcd.colors = o3d.utility.Vector3dVector(valid_colors / 255.0)
    
    return pcd

# 使用KITTI数据集的相机参数
def get_kitti_camera_intrinsics(image_width, image_height):
    """
    获取KITTI数据集的相机内参矩阵
    根据图像尺寸进行缩放
    """
    # KITTI原始相机参数（已归一化）
    K_normalized = np.array([[0.58, 0,    0.5],
                            [0,    1.92, 0.5],
                            [0,    0,    1]], dtype=np.float32)
    
    # 根据实际图像尺寸进行缩放
    K = K_normalized.copy()
    K[0, 0] *= image_width   # fx
    K[1, 1] *= image_height  # fy
    K[0, 2] *= image_width   # cx
    K[1, 2] *= image_height  # cy
    
    return K

# 使用示例
def save_pointcloud_from_depth(depth_file, color_file, output_file):
    """
    从深度图和彩色图生成并保存点云
    """
    # 加载深度图和彩色图
    depth_map = np.load(depth_file)  # 或者用其他方式加载
    color_image = cv2.imread(color_file)
    color_image = cv2.cvtColor(color_image, cv2.COLOR_BGR2RGB)
    
    # 获取图像尺寸
    height, width = depth_map.shape
    
    # 获取KITTI相机参数
    camera_intrinsics = get_kitti_camera_intrinsics(width, height)
    
    # 生成点云
    pcd = depth_to_pointcloud(depth_map, color_image, camera_intrinsics)
    
    # 保存点云
    o3d.io.write_point_cloud(output_file, pcd)
    print(f"点云已保存到: {output_file}")

# 使用示例
if __name__ == "__main__":
    # 示例调用
    save_pointcloud_from_depth(
        depth_file="path/to/depth.npy",
        color_file="path/to/color.jpg", 
        output_file="output.ply"
    )