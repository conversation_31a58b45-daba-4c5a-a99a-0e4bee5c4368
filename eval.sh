#!/bin/bash

# 激活conda环境
source ~/anaconda3/etc/profile.d/conda.sh
conda activate rtmonodepth

# 设置变量
weights_folder="/mnt/acer/RT-MonoDepth-main/log/small/stereo/self_small-DPTDecoderLite-fixed/models/stage1_weights_0"
data_path="/mnt/acer/kitti_jpg"

# 运行评估
python evaluate_depth_self.py \
--load_weights_folder "$weights_folder" \
--eval_stereo \
--batch_size 1 \
--data_path "$data_path" \
--height 192 \
--width 640 \
--use_acm \
--use_s2conv \
--use_booster
