"""
MiDaS融合模型专用评估脚本
简化版本，专门用于评估MiDaS融合模型
"""

from __future__ import absolute_import, division, print_function

import os
import cv2
import datasets
import numpy as np
import torch
from layers import disp_to_depth
from options import MonodepthOptions
from torch.utils.data import DataLoader
from tqdm import tqdm
from utils import readlines

# 导入MiDaS融合模型
from networks.RTMonoDepth.self.small.RTMonoDepth_midas_fusion import RTMonoDepthMiDaSFusion

cv2.setNumThreads(0)

splits_dir = os.path.join(os.path.dirname(__file__), "splits")
STEREO_SCALE_FACTOR = 5.4

def compute_errors(gt, pred):
    """计算深度估计误差"""
    thresh = np.maximum((gt / pred), (pred / gt))
    a1 = (thresh < 1.25     ).mean()
    a2 = (thresh < 1.25 ** 2).mean()
    a3 = (thresh < 1.25 ** 3).mean()
    rmse = (gt - pred) ** 2
    rmse = np.sqrt(rmse.mean())
    rmse_log = (np.log(gt) - np.log(pred)) ** 2
    rmse_log = np.sqrt(rmse_log.mean())
    abs_rel = np.mean(np.abs(gt - pred) / gt)
    sq_rel = np.mean(((gt - pred) ** 2) / gt)
    return abs_rel, sq_rel, rmse, rmse_log, a1, a2, a3

def batch_post_process_disparity(l_disp, r_disp):
    """后处理视差图"""
    _, h, w = l_disp.shape
    m_disp = 0.5 * (l_disp + r_disp)
    l, _ = np.meshgrid(np.linspace(0, 1, w), np.linspace(0, 1, h))
    l_mask = (1.0 - np.clip(20 * (l - 0.05), 0, 1))[None, ...]
    r_mask = l_mask[:, :, ::-1]
    return r_mask * l_disp + l_mask * r_disp + (1.0 - l_mask - r_mask) * m_disp

def evaluate_midas_fusion(opt):
    """评估MiDaS融合模型"""
    MIN_DEPTH = 1e-3
    MAX_DEPTH = 80

    assert sum((opt.eval_mono, opt.eval_stereo)) == 1, \
        "Please choose mono or stereo evaluation by setting either --eval_mono or --eval_stereo"

    # 检查权重文件夹
    opt.load_weights_folder = os.path.expanduser(opt.load_weights_folder)
    assert os.path.isdir(opt.load_weights_folder), f"Cannot find folder {opt.load_weights_folder}"
    print(f"-> Loading MiDaS fusion model from {opt.load_weights_folder}")

    # 检查权重文件
    encoder_path = os.path.join(opt.load_weights_folder, "encoder.pth")
    fusion_path = os.path.join(opt.load_weights_folder, "fusion.pth")
    
    if not os.path.exists(encoder_path):
        print(f"❌ Encoder weights not found: {encoder_path}")
        return
    
    if not os.path.exists(fusion_path):
        print(f"❌ Fusion weights not found: {fusion_path}")
        return

    # 加载测试文件列表
    filenames = readlines(os.path.join(splits_dir, opt.eval_split, "test_files.txt"))
    
    # 创建MiDaS融合模型
    print("-> Creating MiDaS fusion model...")
    model = RTMonoDepthMiDaSFusion(use_midas_fusion=True).cuda().eval()
    
    # 加载权重
    print("-> Loading weights...")
    encoder_dict = torch.load(encoder_path, map_location='cuda')
    fusion_dict = torch.load(fusion_path, map_location='cuda')
    
    # 准备模型状态字典
    model_state_dict = model.state_dict()
    
    # 加载编码器权重
    encoder_loaded = 0
    for k, v in encoder_dict.items():
        if k not in ['height', 'width', 'use_stereo']:
            full_key = f"encoder.{k}"
            if full_key in model_state_dict:
                model_state_dict[full_key] = v
                encoder_loaded += 1
    
    # 加载融合模块权重
    fusion_loaded = 0
    for k, v in fusion_dict.items():
        if k in model_state_dict and not k.startswith('encoder.'):
            model_state_dict[k] = v
            fusion_loaded += 1
    
    # 应用权重
    model.load_state_dict(model_state_dict, strict=False)
    print(f"-> Loaded {encoder_loaded} encoder parameters and {fusion_loaded} fusion parameters")
    
    # 创建数据加载器
    feed_height = opt.height
    feed_width = opt.width
    
    dataset = datasets.KITTIRAWDataset(
        opt.data_path, filenames, feed_height, feed_width, 
        [0], 4, is_train=False, use_depth_hints=False
    )
    dataloader = DataLoader(
        dataset, opt.batch_size, shuffle=False, 
        num_workers=opt.num_workers, pin_memory=True, drop_last=False
    )

    # 推理
    pred_disps = []
    print(f"-> Computing predictions with size {feed_width}x{feed_height}")

    with torch.no_grad():
        for i, data in enumerate(tqdm(dataloader)):
            input_color = data[("color", 0, 0)].cuda()
            
            if opt.post_process:
                input_color = torch.cat((input_color, torch.flip(input_color, [3])), 0)
            
            # MiDaS融合模型推理
            output = model(input_color)
            
            # 获取深度输出
            pred_disp, _ = disp_to_depth(output[("disp", 0)], opt.min_depth, opt.max_depth)
            pred_disp = pred_disp.cpu()[:, 0].numpy()
            
            if opt.post_process:
                N = pred_disp.shape[0] // 2
                pred_disp = batch_post_process_disparity(pred_disp[:N], pred_disp[N:, :, ::-1])
            
            pred_disps.append(pred_disp)

    pred_disps = np.concatenate(pred_disps)

    # 保存预测结果（可选）
    if opt.save_pred_disps:
        output_path = os.path.join(opt.load_weights_folder, f"disps_{opt.eval_split}_split.npy")
        print(f"-> Saving predicted disparities to {output_path}")
        np.save(output_path, pred_disps)

    if opt.no_eval:
        print("-> Evaluation disabled. Done.")
        return

    # 加载真值深度
    gt_path = os.path.join(splits_dir, opt.eval_split, "gt_depths.npz")
    gt_depths = np.load(gt_path, fix_imports=True, encoding='latin1', allow_pickle=True)["data"]

    print("-> Evaluating")
    if opt.eval_stereo:
        print(f"   Stereo evaluation - disabling median scaling, scaling by {STEREO_SCALE_FACTOR}")
        opt.disable_median_scaling = True
        opt.pred_depth_scale_factor = STEREO_SCALE_FACTOR
    else:
        print("   Mono evaluation - using median scaling")

    # 计算误差
    errors = []
    ratios = []
    
    for i in tqdm(range(pred_disps.shape[0])):
        gt_depth = gt_depths[i]
        gt_height, gt_width = gt_depth.shape[:2]
        pred_disp = pred_disps[i]
        pred_disp = cv2.resize(pred_disp, (gt_width, gt_height))
        pred_depth = 1 / pred_disp
        
        if opt.eval_split == "eigen":
            mask = np.logical_and(gt_depth > MIN_DEPTH, gt_depth < MAX_DEPTH)
            crop = np.array([0.40810811 * gt_height, 0.99189189 * gt_height,
                             0.03594771 * gt_width,  0.96405229 * gt_width]).astype(np.int32)
            crop_mask = np.zeros(mask.shape)
            crop_mask[crop[0]:crop[1], crop[2]:crop[3]] = 1
            mask = np.logical_and(mask, crop_mask)
        else:
            mask = gt_depth > 0
            
        pred_depth = pred_depth[mask]
        gt_depth = gt_depth[mask]
        pred_depth *= opt.pred_depth_scale_factor
        
        if not opt.disable_median_scaling:
            ratio = np.median(gt_depth) / np.median(pred_depth)
            ratios.append(ratio)
            pred_depth *= ratio
            
        pred_depth[pred_depth < MIN_DEPTH] = MIN_DEPTH
        pred_depth[pred_depth > MAX_DEPTH] = MAX_DEPTH
        errors.append(compute_errors(gt_depth, pred_depth))

    # 输出结果
    if not opt.disable_median_scaling:
        ratios = np.array(ratios)
        med = np.median(ratios)
        print(f" Scaling ratios | med: {med:0.3f} | std: {np.std(ratios / med):0.3f}")
        
    mean_errors = np.array(errors).mean(0)
    
    print("\n🎯 MiDaS融合模型评估结果:")
    print("=" * 80)
    print(f"{'指标':<12} {'值':<10}")
    print("-" * 80)
    print(f"{'abs_rel':<12} {mean_errors[0]:<10.3f}")
    print(f"{'sq_rel':<12} {mean_errors[1]:<10.3f}")
    print(f"{'rmse':<12} {mean_errors[2]:<10.3f}")
    print(f"{'rmse_log':<12} {mean_errors[3]:<10.3f}")
    print(f"{'a1':<12} {mean_errors[4]:<10.3f}")
    print(f"{'a2':<12} {mean_errors[5]:<10.3f}")
    print(f"{'a3':<12} {mean_errors[6]:<10.3f}")
    print("=" * 80)
    
    # 标准格式输出（用于对比）
    print("\n📊 标准格式输出:")
    print("   " + ("{:>8} | " * 7).format("abs_rel", "sq_rel", "rmse", "rmse_log", "a1", "a2", "a3"))
    print(("&{: 8.3f}  " * 7).format(*mean_errors.tolist()) + "\\\\")
    
    print("\n✅ 评估完成!")
    return mean_errors

if __name__ == "__main__":
    options = MonodepthOptions()
    evaluate_midas_fusion(options.parse())
