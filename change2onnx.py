import torch
import torch.onnx
from networks.RTMonoDepth.RTMonoDepth_s import RTMonoDepth

# 定义模型
model = RTMonoDepth()

# 加载权重文件
encoder_weight_path = "/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/s/m_640_192/encoder.pth"
depth_weight_path = "/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/s/m_640_192/depth.pth"

# 加载编码器权重
encoder_state_dict = torch.load(encoder_weight_path, map_location=torch.device('cpu'))

# 移除不需要的键
for key in ["height", "width", "use_stereo"]:
    if key in encoder_state_dict:
        del encoder_state_dict[key]

model.encoder.load_state_dict(encoder_state_dict)
print(f"编码器权重文件已加载：{encoder_weight_path}")

# 加载解码器权重
depth_state_dict = torch.load(depth_weight_path, map_location=torch.device('cpu'))
model.decoder.load_state_dict(depth_state_dict)  # 使用正确的属性名
print(f"解码器权重文件已加载：{depth_weight_path}")

# 将模型设置为评估模式
model = model.to('cpu')  # 确保模型在 CPU 上
model.eval()

# 创建一个随机输入张量，假设输入图像大小为 192x640
input_tensor = torch.randn(1, 3, 192, 640, device='cpu')  # 确保输入张量在 CPU 上

# 指定 ONNX 文件路径
onnx_file_path = "RTMonoDepth_s.onnx"

# 导出模型为 ONNX 格式
torch.onnx.export(
    model,  # PyTorch 模型
    input_tensor,  # 模型输入（或多个输入的元组）
    onnx_file_path,  # ONNX 文件路径
    export_params=True,  # 导出模型参数
    opset_version=11,  # ONNX 版本（根据需要选择）
    do_constant_folding=True,  # 是否执行常量折叠优化
    input_names=["input"],  # 输入节点名称
    output_names=["output"],  # 输出节点名称
    dynamic_axes={"input": {0: "batch_size"}, "output": {0: "batch_size"}}  # 动态轴
)

print(f"模型已成功导出为 ONNX 格式，保存路径为：{onnx_file_path}")