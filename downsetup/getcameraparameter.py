import cv2
import numpy as np
import glob

# 棋盘格内角点数量 (行, 列) — 这是角点数，不是格子数
pattern_size = (9, 6)

# 实际棋盘格方格尺寸 (单位：mm或m，根据实际测量)
square_size = 25.0  # 25毫米

# 准备棋盘格3D点坐标，z=0平面上
objp = np.zeros((pattern_size[0]*pattern_size[1], 3), np.float32)
objp[:, :2] = np.mgrid[0:pattern_size[0], 0:pattern_size[1]].T.reshape(-1, 2)
objp *= square_size

# 存储所有图片的3D点和2D点
objpoints = []  # 3D点 (世界坐标)
imgpoints = []  # 2D点 (图像坐标)

# 读取所有标定图片（jpg/png等格式）
images = glob.glob('calib_images/*.jpg')  # 改成你的标定图片路径

for fname in images:
    img = cv2.imread(fname)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 查找棋盘格角点
    ret, corners = cv2.findChessboardCorners(gray, pattern_size, None)

    if ret:
        objpoints.append(objp)

        # 精确角点检测
        corners2 = cv2.cornerSubPix(gray, corners, (11,11), (-1,-1),
                                    criteria=(cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
        imgpoints.append(corners2)

        # 可视化角点
        cv2.drawChessboardCorners(img, pattern_size, corners2, ret)
        cv2.imshow('Corners', img)
        cv2.waitKey(500)
    else:
        print(f"未找到角点: {fname}")

cv2.destroyAllWindows()

# 标定相机
ret, K, dist_coeffs, rvecs, tvecs = cv2.calibrateCamera(
    objpoints, imgpoints, gray.shape[::-1], None, None)

print("相机内参矩阵 K = \n", K)
print("\n畸变系数 dist_coeffs = \n", dist_coeffs)

# 计算重投影误差（越小越好）
mean_error = 0
for i in range(len(objpoints)):
    imgpoints2, _ = cv2.projectPoints(objpoints[i], rvecs[i], tvecs[i], K, dist_coeffs)
    error = cv2.norm(imgpoints[i], imgpoints2, cv2.NORM_L2)/len(imgpoints2)
    mean_error += error

print("\n平均重投影误差:", mean_error/len(objpoints))
