import torch
import torch.nn as nn
import torch.nn.functional as F
'''
ACMBlock：用于注意力压缩；

S2Conv：提升速度与效率；

BoosterNet：训练阶段输出边缘；

DPTDecoderLite：模仿 MiDaS3.1 的结构；

Edge-aware Loss：训练时用于边缘精细化监督；

RTMonoDepthFusion：整合模型主类。
'''
# -----------------------------
# ACM Block (Attention Condensed Module)
# -----------------------------
class ACMBlock(nn.Module):
    def __init__(self, in_channels, out_channels, reduction=4):
        super(ACMBlock, self).__init__()
        mid_channels = in_channels // reduction
        self.conv1 = nn.Conv2d(in_channels, mid_channels, 1, bias=False)
        self.act1 = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(mid_channels, in_channels, 1, bias=False)
        self.sigmoid = nn.Sigmoid()
        self.out_proj = nn.Conv2d(in_channels, out_channels, 3, 1, 1)

    def forward(self, x):
        w = F.adaptive_avg_pool2d(x, 1)
        w = self.act1(self.conv1(w))
        w = self.sigmoid(self.conv2(w))
        x = x * w
        return self.out_proj(x)


# -----------------------------
# S2Conv (Separated 2D Conv)
# -----------------------------
class S2Conv(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(S2Conv, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=(3, 1), padding=(1, 0))
        self.act = nn.ELU(inplace=True)

    def forward(self, x):
        x = self.conv1(x)
        x = self.conv2(x)
        return self.act(x)


# -----------------------------
# BoosterNet (Only during training)
# -----------------------------
class BoosterNet(nn.Module):
    def __init__(self, in_channels):
        super(BoosterNet, self).__init__()
        self.edge_conv = nn.Sequential(
            nn.Conv2d(in_channels, 16, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(16, 1, 1)
        )

    def forward(self, x):
        return self.edge_conv(x)


# -----------------------------
# Depth Encoder (Same as original)
# -----------------------------
class DepthEncoder(nn.Module):
    def __init__(self):
        super(DepthEncoder, self).__init__()
        self.num_ch_enc = [64, 64, 128, 192]
        self.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.relu = nn.ReLU(inplace=True)

        self.convs = nn.ModuleList()
        ch_in_out = zip(self.num_ch_enc[:-1], self.num_ch_enc[1:])
        for ch_in, ch_out in ch_in_out:
            self.convs.append(
                nn.Sequential(
                    nn.Conv2d(ch_in, ch_out, kernel_size=3, stride=2, padding=1),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(ch_out, ch_out, kernel_size=3, padding=1),
                    nn.ReLU(inplace=True)
                )
            )

    def forward(self, x):
        features = []
        x = (x - 0.45) / 0.225
        x = self.relu(self.conv1(x))
        features.append(x)
        for conv in self.convs:
            x = conv(x)
            features.append(x)
        return features


# -----------------------------
# DPT-style Lite Decoder with ACM + S2Conv + BoosterNet
# -----------------------------
class DPTDecoderLite(nn.Module):
    def __init__(self, in_channels_list, use_booster=True, use_acm=True, use_s2conv=True):
        super(DPTDecoderLite, self).__init__()
        self.use_booster = use_booster
        self.use_acm = use_acm
        self.use_s2conv = use_s2conv
        self.up_blocks = nn.ModuleList()

        for i in range(len(in_channels_list) - 1, 0, -1):
            block = []
            if self.use_acm:
                block.append(ACMBlock(in_channels_list[i], in_channels_list[i - 1]))
            else:
                block.append(nn.Conv2d(in_channels_list[i], in_channels_list[i - 1], 3, 1, 1))
            if self.use_s2conv:
                block.append(S2Conv(in_channels_list[i - 1], in_channels_list[i - 1]))
            else:
                block.append(nn.ReLU(inplace=True))
            block.append(nn.Upsample(scale_factor=2, mode="bilinear", align_corners=False))
            self.up_blocks.append(nn.Sequential(*block))

        final_block = []
        if self.use_s2conv:
            final_block.append(S2Conv(in_channels_list[0], in_channels_list[0]))
        else:
            final_block.append(nn.ReLU(inplace=True))
        final_block.append(nn.Conv2d(in_channels_list[0], 1, 3, 1, 1))
        final_block.append(nn.ELU(inplace=True))  # 将Sigmoid改为ELU
        self.final_conv = nn.Sequential(*final_block)

        if use_booster:
            self.booster = BoosterNet(in_channels_list[0])

    def forward(self, feats):
        x = feats[-1]
        for i, block in enumerate(self.up_blocks):
            x = block(x)
            x = x + feats[-(i + 2)]  # Skip connection

        depth = self.final_conv(x)

        if self.use_booster and self.training:
            edge = self.booster(x)
            return depth, edge
        return depth

class RTMonoDepthFusion(nn.Module):
    def __init__(self, encoder,
                 use_booster=True,
                 use_acm=True,
                 use_s2conv=True):
        super(RTMonoDepthFusion, self).__init__()
        self.encoder = encoder
        self.use_booster = use_booster
        self.use_acm = use_acm
        self.use_s2conv = use_s2conv

        self.decoder = DPTDecoderLite(self.encoder.num_ch_enc,
                                      use_booster=self.use_booster,
                                      use_acm=self.use_acm,
                                      use_s2conv=self.use_s2conv)

    def forward(self, input_image):
        features = self.encoder(input_image)
        decoder_output = self.decoder(features)
        
        # 将 tuple 转换为 dict 格式
        if isinstance(decoder_output, tuple):
            depth, edge = decoder_output
            outputs = {("disp", 0): depth, ("edge", 0): edge}
        else:
            outputs = {("disp", 0): decoder_output}
        
        return outputs
