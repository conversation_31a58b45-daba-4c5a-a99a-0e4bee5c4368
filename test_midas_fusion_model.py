#!/usr/bin/env python3
"""
测试MiDaS融合模型是否能正常创建和加载权重
"""

import torch
import os
import sys

def test_model_creation():
    """测试模型创建"""
    print("🧪 测试MiDaS融合模型创建...")
    
    try:
        from networks.RTMonoDepth.self.small.RTMonoDepth_midas_fusion import RTMonoDepthMiDaSFusion
        print("✅ 成功导入MiDaS融合模型")
        
        # 创建模型
        model = RTMonoDepthMiDaSFusion(use_midas_fusion=True)
        print("✅ 成功创建MiDaS融合模型")
        
        # 检查模型结构
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 模型参数统计:")
        print(f"   总参数: {total_params:,}")
        print(f"   可训练参数: {trainable_params:,}")
        print(f"   模型大小: {total_params * 4 / 1024**2:.1f} MB (FP32)")
        
        # 测试前向传播
        model.eval()
        test_input = torch.randn(1, 3, 192, 640)
        
        with torch.no_grad():
            outputs = model(test_input)
        
        print(f"✅ 前向传播测试通过")
        print(f"   输入形状: {test_input.shape}")
        print(f"   输出数量: {len(outputs)}")
        
        for key, value in outputs.items():
            print(f"   {key}: {value.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_loading():
    """测试权重加载"""
    print("\n🔧 测试权重加载...")
    
    weights_folder = "/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/s/ms_640_192"
    
    if not os.path.exists(weights_folder):
        print(f"❌ 权重文件夹不存在: {weights_folder}")
        return False
    
    # 检查权重文件
    required_files = ["encoder.pth", "depth.pth"]
    for file_name in required_files:
        file_path = os.path.join(weights_folder, file_name)
        if not os.path.exists(file_path):
            print(f"❌ 权重文件不存在: {file_path}")
            return False
        else:
            size_mb = os.path.getsize(file_path) / 1024 / 1024
            print(f"✅ {file_name}: {size_mb:.1f} MB")
    
    try:
        from networks.RTMonoDepth.self.small.RTMonoDepth_midas_fusion import RTMonoDepthMiDaSFusion
        
        # 创建模型
        model = RTMonoDepthMiDaSFusion(use_midas_fusion=True)
        
        # 加载权重
        encoder_path = os.path.join(weights_folder, "encoder.pth")
        depth_path = os.path.join(weights_folder, "depth.pth")
        
        encoder_dict = torch.load(encoder_path, map_location='cpu')
        depth_dict = torch.load(depth_path, map_location='cpu')
        
        print(f"📥 编码器权重: {len(encoder_dict)} 个参数")
        print(f"📥 解码器权重: {len(depth_dict)} 个参数")
        
        # 获取模型状态字典
        model_state_dict = model.state_dict()
        
        # 映射编码器权重
        encoder_loaded = 0
        for k, v in encoder_dict.items():
            if k not in ['height', 'width', 'use_stereo']:
                full_key = f"encoder.{k}"
                if full_key in model_state_dict:
                    model_state_dict[full_key] = v
                    encoder_loaded += 1
        
        # 映射解码器权重
        decoder_loaded = 0
        for k, v in depth_dict.items():
            decoder_key = f"decoder.{k}" if not k.startswith('decoder.') else k
            if decoder_key in model_state_dict and v.shape == model_state_dict[decoder_key].shape:
                model_state_dict[decoder_key] = v
                decoder_loaded += 1
        
        # 应用权重
        model.load_state_dict(model_state_dict, strict=False)
        
        print(f"✅ 权重加载成功:")
        print(f"   编码器: {encoder_loaded} 个参数")
        print(f"   解码器: {decoder_loaded} 个参数")
        
        # 测试加载权重后的前向传播
        model.eval()
        test_input = torch.randn(1, 3, 192, 640)
        
        with torch.no_grad():
            outputs = model(test_input)
        
        print(f"✅ 权重加载后前向传播测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trainer_creation():
    """测试训练器创建"""
    print("\n🏋️ 测试训练器创建...")
    
    try:
        # 模拟命令行参数
        class MockOptions:
            def __init__(self):
                self.model_name = "test_midas_fusion"
                self.log_dir = "./test_log"
                self.data_path = "/mnt/acer/kitti_jpg"
                self.load_weights_folder = "/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/s/ms_640_192"
                self.models_to_load = ["fusion"]
                self.height = 192
                self.width = 640
                self.no_cuda = False
                self.scales = [0, 1, 2]
                self.frame_ids = [0, -1, 1]
                self.use_stereo = True
                self.pose_model_input = "pairs"
                self.pose_model_type = "separate_resnet"
                self.num_layers = 18
                self.weights_init = "pretrained"
                self.batch_size = 2  # 小批量测试
                self.learning_rate = 1e-4
                self.num_epochs = 1  # 只测试1个epoch
                self.scheduler_step_size = 15
                self.disparity_smoothness = 1e-3
                self.num_workers = 4
                self.log_frequency = 10
                self.save_frequency = 1
                self.png = False
                self.dataset = "kitti"
                self.split = "eigen_zhou"
                self.no_ssim = False
                self.v1_multiscale = False
                self.avg_reprojection = False
                self.disable_automasking = False
                self.predictive_mask = False
                self.min_depth = 0.1
                self.max_depth = 100.0
        
        # 创建模拟选项
        opt = MockOptions()
        
        # 检查数据路径
        if not os.path.exists(opt.data_path):
            print(f"❌ 数据路径不存在: {opt.data_path}")
            return False
        
        # 尝试创建训练器（但不实际训练）
        from trainer_midas_fusion import MiDasFusionTrainer
        
        print("🔧 创建训练器...")
        trainer = MiDasFusionTrainer(opt)
        
        print("✅ 训练器创建成功")
        print(f"   设备: {trainer.device}")
        print(f"   模型数量: {len(trainer.models)}")
        print(f"   训练参数数量: {len(trainer.parameters_to_train)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 MiDaS融合模型测试套件")
    print("=" * 50)
    
    # 测试1: 模型创建
    test1_passed = test_model_creation()
    
    # 测试2: 权重加载
    test2_passed = test_weight_loading()
    
    # 测试3: 训练器创建
    test3_passed = test_trainer_creation()
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    print(f"   模型创建: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"   权重加载: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"   训练器创建: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 所有测试通过！可以开始训练了。")
        return True
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
