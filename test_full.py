from __future__ import absolute_import, division, print_function

import os

import cv2
import datasets
import networks
import numpy as np
import torch
from layers import disp_to_depth
# from networks.RTMonoDepth.RTMonoDepth_s import DepthDecoder, DepthEncoder
from networks.RTMonoDepth.RTMonoDepth import DepthDecoder, DepthEncoder
from options import MonodepthOptions
from torch.utils.data import DataLoader
from tqdm import tqdm
from utils import readlines

# decoder_weights = torch.load('/mnt/acer/RT-MonoDepth-main/log/full_ms/models/weights_8/depth.pth')
# print(decoder_weights.keys())

decoder_weights = torch.load('/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/s/m_640_192/depth.pth')
print(decoder_weights.keys())
#
decoder_weights = torch.load('/mnt/acer/RT-MonoDepth-main/log/small_ms/models/weights_16/depth.pth')
print(decoder_weights.keys())
#
# decoder_weights = torch.load('/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/full/m_640_192/depth.pth')
# print(decoder_weights.keys())
#
# decoder_weights = torch.load('/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/full/s_640_192/depth.pth')
# print(decoder_weights.keys())
#
# decoder_weights = torch.load('/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/full/ms_640_192/depth.pth')
# print(decoder_weights.keys())
#
# decoder_weights = torch.load('/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/full/sh_640_192/depth.pth')
# print(decoder_weights.keys())


# decoder_weights = torch.load('/mnt/acer/RT-MonoDepth-main/log/finetuned_0s_ms/models/weights_6/depth.pth')
# print(decoder_weights.keys())

# decoder_weights = torch.load(decoder_path)
# new_decoder_weights = {}
#
# # 映射 upconv 层的权重
# for i in range(3, -1, -1):
#     # upconv_0
#     new_key = f"convs.('upconv', {i}, 0).conv.conv"
#     old_key = f"decoder.{i*2}.conv"
#     new_decoder_weights[new_key + ".weight"] = decoder_weights[old_key + ".weight"]
#     new_decoder_weights[new_key + ".bias"] = decoder_weights[old_key + ".bias"]
#
#     # upconv_1
#     new_key = f"convs.('upconv', {i}, 1).conv.conv"
#     old_key = f"decoder.{i*2 + 1}.conv"
#     new_decoder_weights[new_key + ".weight"] = decoder_weights[old_key + ".weight"]
#     new_decoder_weights[new_key + ".bias"] = decoder_weights[old_key + ".bias"]
#
# # 映射 dispconv 层的权重
# for s in [0, 1, 2, 3]:
#     new_key = f"convs.('dispconv', {s}).conv1.conv"
#     old_key = f"decoder.{8 + s*3}.conv1"
#     new_decoder_weights[new_key + ".weight"] = decoder_weights[old_key + ".weight"]
#     new_decoder_weights[new_key + ".bias"] = decoder_weights[old_key + ".bias"]
#
#     new_key = f"convs.('dispconv', {s}).conv2.conv"
#     old_key = f"decoder.{8 + s*3 + 1}.conv2"
#     new_decoder_weights[new_key + ".weight"] = decoder_weights[old_key + ".weight"]
#     new_decoder_weights[new_key + ".bias"] = decoder_weights[old_key + ".bias"]
#
#     new_key = f"convs.('dispconv', {s}).conv3.conv"
#     old_key = f"decoder.{8 + s*3 + 2}.conv3"
#     new_decoder_weights[new_key + ".weight"] = decoder_weights[old_key + ".weight"]
#     new_decoder_weights[new_key + ".bias"] = decoder_weights[old_key + ".bias"]
#
# # 加载映射后的权重
# depth_decoder.load_state_dict(new_decoder_weights)

