#!/bin/bash

# 简化的MiDaS融合模型训练启动脚本
# 确保所有依赖都正确配置

echo "🚀 MiDaS融合模型训练启动脚本"
echo "================================"

# 检查conda环境
if ! command -v conda &> /dev/null; then
    echo "❌ Conda未找到，请先安装Anaconda/Miniconda"
    exit 1
fi

# 激活环境
echo "🔧 激活conda环境..."
source ~/anaconda3/etc/profile.d/conda.sh
conda activate rtmonodepth

if [ $? -ne 0 ]; then
    echo "❌ 无法激活rtmonodepth环境，请检查环境是否存在"
    exit 1
fi

echo "✅ 环境激活成功"

# 检查必要文件
echo "🔍 检查必要文件..."

required_files=(
    "trainer_midas_fusion.py"
    "train_midas_fusion.py"
    "networks/RTMonoDepth/self/small/RTMonoDepth_midas_fusion_simple.py"
    "options.py"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少文件: $file"
        exit 1
    fi
done

echo "✅ 必要文件检查通过"

# 检查数据路径
DATA_PATH="/mnt/acer/kitti_jpg"
if [ ! -d "$DATA_PATH" ]; then
    echo "❌ 数据路径不存在: $DATA_PATH"
    echo "请确认KITTI数据集路径正确"
    exit 1
fi

echo "✅ 数据路径检查通过"

# 检查预训练权重
WEIGHTS_PATH="/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/s/ms_640_192"
if [ ! -d "$WEIGHTS_PATH" ]; then
    echo "❌ 预训练权重路径不存在: $WEIGHTS_PATH"
    exit 1
fi

required_weights=("encoder.pth" "depth.pth" "pose_encoder.pth" "pose.pth")
for weight in "${required_weights[@]}"; do
    if [ ! -f "$WEIGHTS_PATH/$weight" ]; then
        echo "❌ 缺少权重文件: $WEIGHTS_PATH/$weight"
        exit 1
    fi
done

echo "✅ 预训练权重检查通过"

# 创建日志目录
LOG_DIR="./log/midas_fusion"
mkdir -p "$LOG_DIR"
echo "✅ 日志目录创建: $LOG_DIR"

# 显示配置信息
echo ""
echo "📋 训练配置:"
echo "   模型: MiDaS融合模型"
echo "   数据: $DATA_PATH"
echo "   权重: $WEIGHTS_PATH"
echo "   日志: $LOG_DIR"
echo "   GPU: $(nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)"
echo ""

# 确认开始训练
read -p "🚀 是否开始训练? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 训练已取消"
    exit 1
fi

echo "🎯 开始训练..."
echo "================================"

# 运行训练
python train_midas_fusion.py \
    --model_name "rtmonodepth_midas_fusion" \
    --log_dir "$LOG_DIR" \
    --data_path "$DATA_PATH" \
    --load_weights_folder "$WEIGHTS_PATH" \
    --models_to_load fusion pose_encoder pose \
    --num_epochs 20 \
    --batch_size 8 \
    --learning_rate 1e-4 \
    --scheduler_step_size 15 \
    --height 192 \
    --width 640 \
    --disparity_smoothness 1e-3 \
    --scales 0 1 2 \
    --frame_ids 0 -1 1 \
    --use_stereo \
    --num_workers 12 \
    --log_frequency 100 \
    --save_frequency 1 \
    --weights_init pretrained

# 检查训练结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 训练完成！"
    echo "📊 权重保存在: $LOG_DIR/rtmonodepth_midas_fusion/models/"
    echo "📈 日志保存在: $LOG_DIR/rtmonodepth_midas_fusion/"
    
    # 询问是否运行评估
    read -p "🔍 是否运行评估? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🏃 开始评估..."
        
        # 找到最新权重
        LATEST_WEIGHTS=$(ls -td $LOG_DIR/rtmonodepth_midas_fusion/models/weights_* 2>/dev/null | head -1)
        
        if [ -n "$LATEST_WEIGHTS" ]; then
            echo "使用权重: $LATEST_WEIGHTS"
            
            python evaluate_depth_self.py \
                --load_weights_folder "$LATEST_WEIGHTS" \
                --eval_stereo \
                --batch_size 4 \
                --data_path "$DATA_PATH" \
                --height 192 \
                --width 640
            
            echo "📊 评估完成！"
        else
            echo "❌ 未找到训练权重文件"
        fi
    fi
else
    echo "❌ 训练失败，请检查错误信息"
    exit 1
fi

echo ""
echo "🎉 所有任务完成！"
