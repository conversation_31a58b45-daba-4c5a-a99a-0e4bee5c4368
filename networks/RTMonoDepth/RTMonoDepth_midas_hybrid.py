"""
RT-MonoDepth with MiDaS预训练编码器集成
针对Jetson Orin NX优化的混合架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from collections import OrderedDict

# MiDaS Swin2-Tiny编码器（简化版本）
class MiDaSSwin2TinyEncoder(nn.Module):
    """
    基于MiDaS Swin2-Tiny的轻量级编码器
    专为Jetson Orin NX优化
    """
    def __init__(self, pretrained_path=None):
        super(MiDaSSwin2TinyEncoder, self).__init__()
        
        # 输出通道数（与MiDaS Swin2-Tiny对应）
        self.num_ch_enc = [96, 192, 384, 768]
        
        # Patch Embedding
        self.patch_embed = nn.Conv2d(3, 96, kernel_size=4, stride=4, padding=0)
        
        # 简化的Swin Transformer块
        self.stages = nn.ModuleList()
        
        # Stage 1: 96 channels, H/4 x W/4
        self.stages.append(self._make_stage(96, 96, 2))
        
        # Stage 2: 192 channels, H/8 x W/8  
        self.stages.append(self._make_stage(96, 192, 2))
        
        # Stage 3: 384 channels, H/16 x W/16
        self.stages.append(self._make_stage(192, 384, 6))
        
        # Stage 4: 768 channels, H/32 x W/32
        self.stages.append(self._make_stage(384, 768, 2))
        
        # 下采样层
        self.downsample_layers = nn.ModuleList([
            nn.Identity(),  # Stage 1不下采样
            nn.Conv2d(96, 192, kernel_size=2, stride=2),   # H/8
            nn.Conv2d(192, 384, kernel_size=2, stride=2),  # H/16  
            nn.Conv2d(384, 768, kernel_size=2, stride=2),  # H/32
        ])
        
        # 加载预训练权重
        if pretrained_path:
            self.load_pretrained(pretrained_path)
    
    def _make_stage(self, in_channels, out_channels, num_blocks):
        """创建简化的Swin Transformer stage"""
        layers = []
        
        # 通道调整
        if in_channels != out_channels:
            layers.append(nn.Conv2d(in_channels, out_channels, 1))
            layers.append(nn.LayerNorm(out_channels))
        
        # 简化的注意力块
        for _ in range(num_blocks):
            layers.extend([
                nn.Conv2d(out_channels, out_channels, 3, 1, 1, groups=out_channels),
                nn.GELU(),
                nn.Conv2d(out_channels, out_channels, 1),
                nn.LayerNorm(out_channels),
            ])
        
        return nn.Sequential(*layers)
    
    def load_pretrained(self, pretrained_path):
        """加载MiDaS预训练权重（需要权重映射）"""
        try:
            pretrained_dict = torch.load(pretrained_path, map_location='cpu')
            # 这里需要实现权重映射逻辑
            # 将MiDaS的权重映射到简化的架构
            print(f"Loaded MiDaS pretrained weights from {pretrained_path}")
        except Exception as e:
            print(f"Warning: Could not load pretrained weights: {e}")
    
    def forward(self, x):
        """前向传播"""
        features = []
        
        # Patch embedding
        x = self.patch_embed(x)  # [B, 96, H/4, W/4]
        
        # 各个stage
        for i, (stage, downsample) in enumerate(zip(self.stages, self.downsample_layers)):
            if i > 0:
                x = downsample(x)
            
            x = stage(x)
            features.append(x)
        
        return features


# 轻量级DPT解码器
class LightDPTDecoder(nn.Module):
    """
    轻量级DPT解码器，针对Jetson优化
    """
    def __init__(self, encoder_channels, scales=range(3)):
        super(LightDPTDecoder, self).__init__()
        
        self.scales = scales
        self.encoder_channels = encoder_channels
        
        # 特征融合模块
        self.fusion_blocks = nn.ModuleList()
        fusion_dim = 256  # 统一的融合维度
        
        for ch in encoder_channels:
            self.fusion_blocks.append(
                nn.Sequential(
                    nn.Conv2d(ch, fusion_dim, 1),
                    nn.BatchNorm2d(fusion_dim),
                    nn.ReLU(inplace=True)
                )
            )
        
        # 上采样和细化模块
        self.refinement_blocks = nn.ModuleList()
        for i in range(len(encoder_channels) - 1):
            self.refinement_blocks.append(
                nn.Sequential(
                    nn.Conv2d(fusion_dim, fusion_dim, 3, 1, 1),
                    nn.BatchNorm2d(fusion_dim),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(fusion_dim, fusion_dim, 3, 1, 1),
                    nn.BatchNorm2d(fusion_dim),
                    nn.ReLU(inplace=True)
                )
            )
        
        # 深度预测头
        self.depth_heads = nn.ModuleDict()
        for s in scales:
            self.depth_heads[f"scale_{s}"] = nn.Sequential(
                nn.Conv2d(fusion_dim, 128, 3, 1, 1),
                nn.ReLU(inplace=True),
                nn.Conv2d(128, 1, 1),
                nn.Sigmoid()
            )
    
    def forward(self, features):
        """前向传播"""
        outputs = {}
        
        # 特征融合
        fused_features = []
        for feat, fusion_block in zip(features, self.fusion_blocks):
            fused_features.append(fusion_block(feat))
        
        # 从最深层开始上采样和融合
        x = fused_features[-1]  # 最深层特征
        
        for i in range(len(fused_features) - 2, -1, -1):
            # 上采样
            x = F.interpolate(x, size=fused_features[i].shape[2:], 
                            mode='bilinear', align_corners=False)
            
            # 特征融合
            x = x + fused_features[i]
            
            # 细化
            if i < len(self.refinement_blocks):
                x = self.refinement_blocks[i](x)
            
            # 生成对应尺度的深度图
            if i in self.scales:
                depth = self.depth_heads[f"scale_{i}"](x)
                outputs[("disp", i)] = depth
        
        return outputs


# 完整的混合模型
class RTMonoDepthMiDaSHybrid(nn.Module):
    """
    RT-MonoDepth + MiDaS混合模型
    针对Jetson Orin NX优化
    """
    def __init__(self, pretrained_midas_path=None, use_lightweight_decoder=True):
        super(RTMonoDepthMiDaSHybrid, self).__init__()
        
        # MiDaS预训练编码器
        self.encoder = MiDaSSwin2TinyEncoder(pretrained_midas_path)
        
        # 选择解码器
        if use_lightweight_decoder:
            self.decoder = LightDPTDecoder(self.encoder.num_ch_enc)
        else:
            # 使用原始RT-MonoDepth解码器
            from .RTMonoDepth_s import DepthDecoder
            self.decoder = DepthDecoder(self.encoder.num_ch_enc)
    
    def forward(self, x):
        """前向传播"""
        features = self.encoder(x)
        outputs = self.decoder(features)
        return outputs


# Jetson优化版本
class RTMonoDepthJetsonOptimized(RTMonoDepthMiDaSHybrid):
    """
    专为Jetson Orin NX优化的版本
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 启用混合精度
        self.use_fp16 = True
        
        # 优化内存使用
        self._optimize_for_jetson()
    
    def _optimize_for_jetson(self):
        """Jetson特定优化"""
        # 启用内存高效的注意力
        for module in self.modules():
            if hasattr(module, 'fused_attention'):
                module.fused_attention = True
    
    def forward(self, x):
        """优化的前向传播"""
        if self.use_fp16 and x.dtype != torch.float16:
            x = x.half()
        
        return super().forward(x)
