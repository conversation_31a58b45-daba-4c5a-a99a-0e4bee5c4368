# Copyright Niantic 2019. Patent Pending. All rights reserved.
#
# This software is licensed under the terms of the Monodepth2 licence
# which allows for non-commercial use only, the full terms of which are made
# available in the LICENSE file.

"""
RT-MonoDepth-S + DPT特征融合
基于Intel DPT官方实现的真实特征融合策略
GitHub: https://github.com/isl-org/DPT
"""

from __future__ import absolute_import, division, print_function

from collections import OrderedDict
import torch
import torch.nn as nn
import torch.nn.functional as F


class DepthEncoder(nn.Module):
    """原始RT-MonoDepth-S编码器，保持不变"""
    def __init__(self):
        super(DepthEncoder, self).__init__()
        self.num_ch_enc = [64, 64, 128, 192]
        self.num_ch_enc_build = [64, 64, 128, 192]
        self.convs = nn.ModuleList()

        self.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.relu = nn.ReLU(inplace=True)

        for l, (ch_in, ch_out) in enumerate(zip(self.num_ch_enc_build[:-1], self.num_ch_enc_build[1:])):
            layer = nn.Sequential(
                nn.Conv2d(ch_in, ch_out, kernel_size=3, stride=2, padding=1, bias=True),
                nn.LeakyReLU(0.1, inplace=True),
                nn.Conv2d(ch_out, ch_out, kernel_size=3, stride=1, padding=1, bias=True),
                nn.LeakyReLU(0.1, inplace=True)
            )
            self.convs.append(layer)

        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        features = []
        x = (x - 0.45) / 0.225
        x = self.conv1(x)
        x = self.relu(x)
        features.append(x)

        for conv in self.convs:
            x = conv(x)
            features.append(x)

        return features


class ResidualConvUnit(nn.Module):
    """
    DPT的残差卷积单元 - 来自官方实现
    https://github.com/isl-org/DPT/blob/main/dpt/blocks.py
    """
    def __init__(self, features, activation=nn.ReLU(True), bn=False):
        super().__init__()
        
        self.bn = bn
        self.groups = 1

        self.conv1 = nn.Conv2d(
            features, features, kernel_size=3, stride=1, padding=1, bias=not self.bn, groups=self.groups
        )
        self.conv2 = nn.Conv2d(
            features, features, kernel_size=3, stride=1, padding=1, bias=not self.bn, groups=self.groups
        )

        if self.bn:
            self.bn1 = nn.BatchNorm2d(features)
            self.bn2 = nn.BatchNorm2d(features)

        self.activation = activation
        self.skip_add = nn.quantized.FloatFunctional()

    def forward(self, x):
        out = self.activation(x)
        out = self.conv1(out)
        if self.bn:
            out = self.bn1(out)

        out = self.activation(out)
        out = self.conv2(out)
        if self.bn:
            out = self.bn2(out)

        return self.skip_add.add(out, x)


class FeatureFusionBlock(nn.Module):
    """
    DPT的特征融合块 - 来自官方实现
    https://github.com/isl-org/DPT/blob/main/dpt/blocks.py
    """
    def __init__(self, features, activation=nn.ReLU(True), deconv=False, bn=False, expand=False, align_corners=True):
        super().__init__()

        self.deconv = deconv
        self.align_corners = align_corners
        self.groups = 1
        self.expand = expand
        
        out_features = features
        if self.expand:
            out_features = features // 2

        self.out_conv = nn.Conv2d(features, out_features, kernel_size=1, stride=1, padding=0, bias=True, groups=1)

        self.resConfUnit1 = ResidualConvUnit(features, activation, bn)
        self.resConfUnit2 = ResidualConvUnit(features, activation, bn)

        self.skip_add = nn.quantized.FloatFunctional()

    def forward(self, *xs):
        output = xs[0]

        if len(xs) == 2:
            res = self.resConfUnit1(xs[1])
            # 确保尺寸匹配
            if output.shape != res.shape:
                res = nn.functional.interpolate(
                    res, size=output.shape[2:], mode="bilinear", align_corners=self.align_corners
                )
            output = self.skip_add.add(output, res)

        output = self.resConfUnit2(output)

        if self.deconv:
            output = nn.functional.interpolate(
                output, scale_factor=2, mode="bilinear", align_corners=self.align_corners
            )

        output = self.out_conv(output)

        return output


class DPTFeatureFusion(nn.Module):
    """
    基于DPT的特征融合模块
    将RT-MonoDepth-S的多尺度特征融合为统一表示
    """
    def __init__(self, encoder_channels=[64, 64, 128, 192], features=256):
        super().__init__()
        
        self.features = features
        
        # 特征投影层 (对应DPT的layer_rn)
        self.projects = nn.ModuleList([
            nn.Conv2d(ch, features, kernel_size=1, stride=1, padding=0)
            for ch in encoder_channels
        ])
        
        # 特征融合块 (对应DPT的refinenet)
        self.refinenet1 = FeatureFusionBlock(features, deconv=True)
        self.refinenet2 = FeatureFusionBlock(features, deconv=True)  
        self.refinenet3 = FeatureFusionBlock(features, deconv=True)
        self.refinenet4 = FeatureFusionBlock(features, deconv=False)
        
        # 输出投影
        self.output_conv = nn.Sequential(
            nn.Conv2d(features, features // 2, kernel_size=3, stride=1, padding=1),
            nn.ReLU(True),
            nn.Conv2d(features // 2, 32, kernel_size=3, stride=1, padding=1),
            nn.ReLU(True),
        )

    def forward(self, encoder_features):
        # 1. 将所有特征投影到统一维度
        layer_1 = self.projects[0](encoder_features[0])  # 1/2
        layer_2 = self.projects[1](encoder_features[1])  # 1/4  
        layer_3 = self.projects[2](encoder_features[2])  # 1/8
        layer_4 = self.projects[3](encoder_features[3])  # 1/16

        # 2. DPT的自顶向下特征融合
        # 从最深层开始融合
        path_4 = self.refinenet4(layer_4)                    # 1/16
        path_3 = self.refinenet3(path_4, layer_3)            # 1/8  
        path_2 = self.refinenet2(path_3, layer_2)            # 1/4
        path_1 = self.refinenet1(path_2, layer_1)            # 1/2

        # 3. 输出投影
        out = self.output_conv(path_1)
        
        return out


class ConvBlock(nn.Module):
    """Layer to perform a convolution followed by ELU"""
    def __init__(self, in_channels, out_channels):
        super(ConvBlock, self).__init__()
        self.pad = nn.ReflectionPad2d(1)
        self.conv = nn.Conv2d(int(in_channels), int(out_channels), 3)
        self.nonlin = nn.ELU(inplace=True)

    def forward(self, x):
        out = self.pad(x)
        out = self.conv(out)
        out = self.nonlin(out)
        return out


class Decoder(nn.Module):
    def __init__(self, in_channels):
        super(Decoder, self).__init__()
        self.in_channels = in_channels
        self.conv3 = nn.Conv2d(in_channels, 1, 3, 1, 1)
        self.relu = nn.LeakyReLU(0.1, inplace=True)

        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        out = self.conv3(x)
        return out


class EnhancedDepthDecoder(nn.Module):
    """
    增强的深度解码器，集成DPT特征融合
    """
    def __init__(self, num_ch_enc, scales=range(3), use_skips=False, use_dpt_fusion=True):
        super(EnhancedDepthDecoder, self).__init__()

        self.use_skips = use_skips
        self.scales = scales
        self.use_dpt_fusion = use_dpt_fusion
        
        if self.use_dpt_fusion:
            # DPT特征融合模块
            self.dpt_fusion = DPTFeatureFusion(num_ch_enc, features=256)
            # 融合后的特征通道数
            fusion_channels = 32
        else:
            fusion_channels = num_ch_enc[-1]

        # 简化的解码器头部
        self.final_conv = nn.Sequential(
            nn.Conv2d(fusion_channels, 64, kernel_size=3, padding=1),
            nn.ReLU(True),
            nn.Conv2d(64, 32, kernel_size=3, padding=1), 
            nn.ReLU(True),
            nn.Conv2d(32, 16, kernel_size=3, padding=1),
            nn.ReLU(True),
        )
        
        # 深度预测头
        self.depth_heads = nn.ModuleDict()
        for s in self.scales:
            self.depth_heads[f"scale_{s}"] = Decoder(16)
        
        self.sigmoid = nn.Sigmoid()

    def forward(self, input_features):
        outputs = {}
        
        if self.use_dpt_fusion:
            # 使用DPT特征融合
            fused_features = self.dpt_fusion(input_features)
            
            # 上采样到原始分辨率的1/2
            fused_features = F.interpolate(
                fused_features, scale_factor=2, mode="bilinear", align_corners=True
            )
        else:
            # 使用原始特征
            fused_features = input_features[-1]
        
        # 最终卷积处理
        x = self.final_conv(fused_features)
        
        # 生成多尺度深度图
        for s in self.scales:
            if s > 0:
                # 下采样到对应尺度
                scale_features = F.interpolate(
                    x, scale_factor=0.5**s, mode="bilinear", align_corners=True
                )
            else:
                scale_features = x
                
            depth = self.sigmoid(self.depth_heads[f"scale_{s}"](scale_features))
            outputs[("disp", s)] = depth

        return outputs


class RTMonoDepthSDPT(nn.Module):
    """
    RT-MonoDepth-S + DPT特征融合完整模型
    """
    def __init__(self, use_dpt_fusion=True):
        super(RTMonoDepthSDPT, self).__init__()
        
        self.encoder = DepthEncoder()
        self.decoder = EnhancedDepthDecoder(
            self.encoder.num_ch_enc, 
            use_dpt_fusion=use_dpt_fusion
        )
        
        print(f"RTMonoDepthSDPT initialized with DPT fusion: {use_dpt_fusion}")

    def forward(self, x):
        features = self.encoder(x)
        outputs = self.decoder(features)
        return outputs


# 工厂函数
def create_rtmonodepth_s_dpt(use_dpt_fusion=True):
    """创建RT-MonoDepth-S + DPT模型"""
    return RTMonoDepthSDPT(use_dpt_fusion=use_dpt_fusion)
