"""
MiDaS特征融合模块
可以插入到现有RT-MonoDepth架构中
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class MiDaSFeatureFusion(nn.Module):
    """
    MiDaS风格的特征融合模块
    可以集成到现有的RT-MonoDepth中
    """
    def __init__(self, input_channels, output_channels=256):
        super(MiDaSFeatureFusion, self).__init__()
        
        self.input_channels = input_channels
        self.output_channels = output_channels
        
        # 特征投影层
        self.projections = nn.ModuleList()
        for ch in input_channels:
            self.projections.append(
                nn.Sequential(
                    nn.Conv2d(ch, output_channels, 1, bias=False),
                    nn.BatchNorm2d(output_channels),
                    nn.ReLU(inplace=True)
                )
            )
        
        # 特征细化模块（类似DPT的Reassemble）
        self.reassemble_blocks = nn.ModuleList()
        for i in range(len(input_channels)):
            self.reassemble_blocks.append(
                nn.Sequential(
                    nn.Conv2d(output_channels, output_channels, 3, 1, 1, groups=output_channels),
                    nn.Conv2d(output_channels, output_channels, 1),
                    nn.BatchNorm2d(output_channels),
                    nn.ReLU(inplace=True)
                )
            )
        
        # 跨尺度注意力模块
        self.cross_scale_attention = CrossScaleAttention(output_channels, len(input_channels))
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Conv2d(output_channels * len(input_channels), output_channels, 1),
            nn.BatchNorm2d(output_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(output_channels, output_channels, 3, 1, 1),
            nn.BatchNorm2d(output_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, features):
        """
        Args:
            features: List of feature maps from encoder
        Returns:
            fused_features: List of enhanced feature maps
        """
        # 1. 特征投影到统一维度
        projected_features = []
        for feat, proj in zip(features, self.projections):
            projected_features.append(proj(feat))
        
        # 2. 特征细化
        refined_features = []
        for feat, reassemble in zip(projected_features, self.reassemble_blocks):
            refined_features.append(reassemble(feat))
        
        # 3. 跨尺度注意力
        attended_features = self.cross_scale_attention(refined_features)
        
        # 4. 多尺度特征融合
        # 将所有特征上采样到最大分辨率
        target_size = attended_features[0].shape[2:]
        upsampled_features = []
        
        for feat in attended_features:
            if feat.shape[2:] != target_size:
                feat = F.interpolate(feat, size=target_size, mode='bilinear', align_corners=False)
            upsampled_features.append(feat)
        
        # 5. 特征拼接和最终融合
        concatenated = torch.cat(upsampled_features, dim=1)
        fused = self.final_fusion(concatenated)
        
        # 6. 生成多尺度输出
        output_features = []
        for i, original_feat in enumerate(features):
            # 将融合特征下采样到原始尺度
            target_size = original_feat.shape[2:]
            if fused.shape[2:] != target_size:
                scaled_fused = F.interpolate(fused, size=target_size, mode='bilinear', align_corners=False)
            else:
                scaled_fused = fused
            
            # 残差连接
            enhanced_feat = scaled_fused + attended_features[i]
            output_features.append(enhanced_feat)
        
        return output_features


class CrossScaleAttention(nn.Module):
    """
    跨尺度注意力模块
    让不同尺度的特征相互关注
    """
    def __init__(self, channels, num_scales):
        super(CrossScaleAttention, self).__init__()
        
        self.channels = channels
        self.num_scales = num_scales
        
        # 注意力权重生成
        self.attention_conv = nn.Conv2d(channels * num_scales, num_scales, 1)
        self.softmax = nn.Softmax(dim=1)
        
        # 特征增强
        self.enhancement = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(channels, channels, 3, 1, 1),
                nn.BatchNorm2d(channels),
                nn.ReLU(inplace=True)
            ) for _ in range(num_scales)
        ])
    
    def forward(self, features):
        """
        Args:
            features: List of feature maps with same channels but different sizes
        Returns:
            attended_features: List of attention-enhanced features
        """
        # 将所有特征上采样到最大分辨率
        max_size = max([feat.shape[2:] for feat in features])
        upsampled_features = []
        
        for feat in features:
            if feat.shape[2:] != max_size:
                feat = F.interpolate(feat, size=max_size, mode='bilinear', align_corners=False)
            upsampled_features.append(feat)
        
        # 计算跨尺度注意力权重
        concatenated = torch.cat(upsampled_features, dim=1)
        attention_weights = self.softmax(self.attention_conv(concatenated))
        
        # 应用注意力权重
        attended_features = []
        for i, (feat, enhance) in enumerate(zip(upsampled_features, self.enhancement)):
            # 获取对应的注意力权重
            weight = attention_weights[:, i:i+1, :, :]
            
            # 应用注意力和增强
            attended_feat = enhance(feat * weight)
            attended_features.append(attended_feat)
        
        return attended_features


class MiDaSDepthHead(nn.Module):
    """
    MiDaS风格的深度预测头
    """
    def __init__(self, input_channels, intermediate_channels=128):
        super(MiDaSDepthHead, self).__init__()
        
        self.depth_head = nn.Sequential(
            # 第一层：特征细化
            nn.Conv2d(input_channels, intermediate_channels, 3, 1, 1),
            nn.BatchNorm2d(intermediate_channels),
            nn.ReLU(inplace=True),
            
            # 第二层：进一步细化
            nn.Conv2d(intermediate_channels, intermediate_channels, 3, 1, 1),
            nn.BatchNorm2d(intermediate_channels),
            nn.ReLU(inplace=True),
            
            # 第三层：深度预测
            nn.Conv2d(intermediate_channels, 1, 1),
            nn.Sigmoid()  # 输出0-1范围的视差
        )
    
    def forward(self, x):
        return self.depth_head(x)


# 集成到现有RT-MonoDepth的包装器
class RTMonoDepthWithMiDaSFusion(nn.Module):
    """
    在现有RT-MonoDepth基础上添加MiDaS特征融合
    """
    def __init__(self, original_encoder, original_decoder, fusion_channels=256):
        super(RTMonoDepthWithMiDaSFusion, self).__init__()
        
        self.encoder = original_encoder
        self.original_decoder = original_decoder
        
        # MiDaS特征融合模块
        self.midas_fusion = MiDaSFeatureFusion(
            input_channels=original_encoder.num_ch_enc,
            output_channels=fusion_channels
        )
        
        # 增强的深度预测头
        self.enhanced_depth_heads = nn.ModuleDict()
        for scale in [0, 1, 2]:
            self.enhanced_depth_heads[f"scale_{scale}"] = MiDaSDepthHead(fusion_channels)
    
    def forward(self, x):
        # 1. 编码器特征提取
        encoder_features = self.encoder(x)
        
        # 2. MiDaS特征融合
        enhanced_features = self.midas_fusion(encoder_features)
        
        # 3. 原始解码器输出（作为基线）
        original_outputs = self.original_decoder(encoder_features)
        
        # 4. 增强的深度预测
        enhanced_outputs = {}
        for scale, head in self.enhanced_depth_heads.items():
            scale_idx = int(scale.split('_')[1])
            if scale_idx < len(enhanced_features):
                enhanced_outputs[("disp", scale_idx)] = head(enhanced_features[scale_idx])
        
        # 5. 融合原始和增强的输出
        final_outputs = {}
        for key in original_outputs.keys():
            if key in enhanced_outputs:
                # 加权融合
                final_outputs[key] = 0.7 * enhanced_outputs[key] + 0.3 * original_outputs[key]
            else:
                final_outputs[key] = original_outputs[key]
        
        return final_outputs
