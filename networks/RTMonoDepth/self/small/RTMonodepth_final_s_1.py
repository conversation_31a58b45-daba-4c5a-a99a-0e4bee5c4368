import torch
import torch.nn as nn
import torch.nn.functional as F
import timm
from collections import OrderedDict
import os

'''
ACMBlock：用于注意力压缩；

S2Conv：提升速度与效率；

BoosterNet：训练阶段输出边缘；

DPTDecoderLite：模仿 MiDaS3.1 的结构；

Edge-aware Loss：训练时用于边缘精细化监督；

RTMonoDepthFusion：整合模型主类。
'''
# -----------------------------
# ACM Block (Attention Condensed Module)
# -----------------------------
class ACMBlock(nn.Module):
    def __init__(self, in_channels, out_channels, reduction=4):
        super(ACMBlock, self).__init__()
        # 确保 reduction 和通道数计算正确
        self.reduction = min(reduction, in_channels // 2)  # 限制最大压缩比
        mid_channels = max(8, in_channels // self.reduction)  # 确保最小通道数
        
        # 注意力压缩部分
        self.attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, mid_channels, 1, bias=False),  # in_channels -> mid_channels
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, in_channels, 1, bias=False),  # mid_channels -> in_channels
            nn.Sigmoid()
        )
        
        # 通道调整（如果需要）
        if in_channels != out_channels:
            self.channel_conv = nn.Conv2d(in_channels, out_channels, 1)
        else:
            self.channel_conv = nn.Identity()

    def forward(self, x):
        # 应用注意力
        attn = self.attention(x)
        x = x * attn
        
        # 调整通道数
        x = self.channel_conv(x)
        return x


# -----------------------------
# S2Conv (Separated 2D Conv)
# -----------------------------
class S2Conv(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(S2Conv, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=(3, 1), padding=(1, 0))
        self.act = nn.ELU(inplace=True)

    def forward(self, x):
        x = self.conv1(x)
        x = self.conv2(x)
        return self.act(x)


# -----------------------------
# BoosterNet (Only during training)
# -----------------------------
class BoosterNet(nn.Module):
    def __init__(self, in_channels):
        super(BoosterNet, self).__init__()
        self.edge_conv = nn.Sequential(
            nn.Conv2d(in_channels, 16, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(16, 1, 1)
        )

    def forward(self, x):
        return self.edge_conv(x)


# -----------------------------
# Depth Encoder (Same as original)
# -----------------------------
class DepthEncoder(nn.Module):
    def __init__(self):
        super(DepthEncoder, self).__init__()
        self.num_ch_enc = [64, 64, 128, 192]
        self.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.relu = nn.ReLU(inplace=True)

        self.convs = nn.ModuleList()
        ch_in_out = zip(self.num_ch_enc[:-1], self.num_ch_enc[1:])
        for ch_in, ch_out in ch_in_out:
            self.convs.append(
                nn.Sequential(
                    nn.Conv2d(ch_in, ch_out, kernel_size=3, stride=2, padding=1),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(ch_out, ch_out, kernel_size=3, padding=1),
                    nn.ReLU(inplace=True)
                )
            )

    def forward(self, x):
        features = []
        x = (x - 0.45) / 0.225
        x = self.relu(self.conv1(x))
        features.append(x)
        for conv in self.convs:
            x = conv(x)
            features.append(x)
        return features


# -----------------------------
# DPT-style Lite Decoder with ACM + S2Conv + BoosterNet
# -----------------------------
class DPTDecoderLite(nn.Module):
    def __init__(self, num_ch_enc, scales=range(3), use_booster=True, use_acm=True, use_s2conv=True):
        super(DPTDecoderLite, self).__init__()
        self.use_booster = use_booster
        self.use_acm = use_acm
        self.use_s2conv = use_s2conv
        self.num_ch_enc = num_ch_enc

        # 修正通道数顺序：从大到小
        self.num_ch_dec = [192, 96, 64, 32, 16]  # 修改顺序
        self.scales = scales

        # 跳跃连接的通道调整层
        # 根据空间尺寸匹配：i=4连接features[2], i=3连接features[1], i=2连接features[0]
        self.skip_convs = nn.ModuleDict()
        skip_connections = {4: 2, 3: 1, 2: 0}  # decoder_level: encoder_level
        for dec_level, enc_level in skip_connections.items():
            dec_ch = self.num_ch_dec[4-dec_level]  # 解码器在该层的通道数
            enc_ch = num_ch_enc[enc_level]  # 编码器在该层的通道数
            if dec_ch != enc_ch:
                self.skip_convs[str(dec_level)] = nn.Conv2d(enc_ch, dec_ch, 1)

        # 上采样块
        self.up_blocks = nn.ModuleList()
        for i in range(4, -1, -1):
            # upconv_0: 从高层到低层
            num_ch_in = num_ch_enc[-1] if i == 4 else self.num_ch_dec[4-i-1]
            num_ch_out = self.num_ch_dec[4-i]

            block = []
            # 第一个卷积块
            if self.use_acm:
                block.append(ACMBlock(num_ch_in, num_ch_out))
            else:
                block.append(nn.Conv2d(num_ch_in, num_ch_out, 3, 1, 1))

            if self.use_s2conv:
                block.append(S2Conv(num_ch_out, num_ch_out))
            else:
                block.append(nn.ReLU(inplace=True))

            block.append(nn.Upsample(scale_factor=2, mode="bilinear", align_corners=False))
            self.up_blocks.append(nn.Sequential(*block))

            # upconv_1
            block = []
            if self.use_acm:
                block.append(ACMBlock(num_ch_out, num_ch_out))
            else:
                block.append(nn.Conv2d(num_ch_out, num_ch_out, 3, 1, 1))

            if self.use_s2conv:
                block.append(S2Conv(num_ch_out, num_ch_out))
            else:
                block.append(nn.ReLU(inplace=True))

            self.up_blocks.append(nn.Sequential(*block))

        # 输出卷积层
        self.dispconv = nn.ModuleList()
        for s in self.scales:
            conv = nn.Sequential(
                nn.Conv2d(self.num_ch_dec[4-s], 1, 3, 1, 1),  # 修正索引
                nn.ELU(inplace=True)
            )
            self.dispconv.append(conv)

        if use_booster:
            self.booster = BoosterNet(self.num_ch_dec[-1])  # 使用最小通道数

    def forward(self, features):
        outputs = {}
        x = features[-1]  # 从最高层开始

        # 跳跃连接映射：decoder_level -> encoder_level
        skip_connections = {4: 2, 3: 1, 2: 0}

        for i in range(4, -1, -1):
            # upconv_0 - 修正索引计算
            block_idx = 2 * (4 - i)
            x = self.up_blocks[block_idx](x)

            # 添加跳跃连接（带通道调整）
            if i in skip_connections:
                enc_level = skip_connections[i]
                skip_feat = features[enc_level]
                # 如果需要通道调整
                if str(i) in self.skip_convs:
                    skip_feat = self.skip_convs[str(i)](skip_feat)
                x = x + skip_feat

            # upconv_1 - 修正索引计算
            x = self.up_blocks[block_idx + 1](x)

            # 生成对应尺度的深度图
            if i in self.scales:
                depth = self.dispconv[self.scales.index(i)](x)
                outputs[("disp", i)] = depth

        if self.use_booster and self.training:
            edge = self.booster(x)
            outputs[("edge", 0)] = edge

        return outputs

# -----------------------------
# DPT Hybrid Encoder
# -----------------------------
class DPTHybridEncoder(nn.Module):
    def __init__(self):
        super(DPTHybridEncoder, self).__init__()
        # 使用与原始编码器相同的通道数，以便兼容原始权重
        self.num_ch_enc = [64, 64, 128, 192]
        
        # 加载 DPT-Hybrid 主干
        self.backbone = timm.create_model(
            'vit_hybrid_base_patch16_384',
            pretrained=False,
            features_only=True,
            out_indices=(0, 1, 2, 3)
        )
        
        # 添加通道调整层以匹配原始编码器的通道数
        self.channel_adjust = nn.ModuleList([
            nn.Conv2d(96, 64, 1),
            nn.Conv2d(192, 64, 1),
            nn.Conv2d(384, 128, 1),
            nn.Conv2d(768, 192, 1)
        ])

    def forward(self, x):
        features = []
        x = (x - 0.45) / 0.225
        
        # 获取backbone特征
        feats = self.backbone(x)
        
        # 调整通道数以匹配原始编码器
        for feat, adjust in zip(feats, self.channel_adjust):
            features.append(adjust(feat))
        
        return features

# -----------------------------
# Lite DPT Hybrid Decoder
# -----------------------------
class LiteDPTHybridDecoder(nn.Module):
    def __init__(self, num_ch_enc, use_booster=True, use_acm=True, use_s2conv=True):
        super(LiteDPTHybridDecoder, self).__init__()
        self.use_booster = use_booster
        self.use_acm = use_acm
        self.use_s2conv = use_s2conv
        self.num_ch_enc = num_ch_enc
        
        # 特征融合层
        self.fusion_blocks = nn.ModuleList()
        for i in range(len(num_ch_enc) - 1, 0, -1):
            block = []
            
            # 添加 ACM
            if use_acm:
                block.append(ACMBlock(num_ch_enc[i], num_ch_enc[i-1]))
            else:
                block.append(nn.Conv2d(num_ch_enc[i], num_ch_enc[i-1], 1))
            
            # 添加 S2Conv
            if use_s2conv:
                block.append(S2Conv(num_ch_enc[i-1], num_ch_enc[i-1]))
            
            # 上采样
            block.append(nn.Upsample(scale_factor=2, mode='bilinear', align_corners=False))
            
            self.fusion_blocks.append(nn.Sequential(*block))
        
        # 最终输出层
        final_block = []
        if use_s2conv:
            final_block.append(S2Conv(num_ch_enc[0], num_ch_enc[0]))
        final_block.extend([
            nn.Conv2d(num_ch_enc[0], 1, 3, padding=1),
            nn.ELU(inplace=True)
        ])
        self.final_conv = nn.Sequential(*final_block)
        
        # 边缘增强模块
        if use_booster:
            self.booster = BoosterNet(num_ch_enc[0])

    def forward(self, features):
        x = features[-1]
        
        # 特征融合
        for i, fusion_block in enumerate(self.fusion_blocks):
            x = fusion_block(x)
            x = x + features[-(i+2)]  # 跳跃连接
        
        # 输出深度图
        depth = self.final_conv(x)
        
        if self.use_booster and self.training:
            edge = self.booster(x)
            return depth, edge
        return depth

# -----------------------------
# RTMonoDepth Fusion
# -----------------------------
class RTMonoDepthFusion(nn.Module):
    def __init__(self, encoder,  # 直接接收编码器实例
                 use_booster=True,
                 use_acm=True,
                 use_s2conv=True):
        super(RTMonoDepthFusion, self).__init__()
        self.encoder = encoder
        
        # 使用新的 DPTDecoderLite，传入正确的参数
        self.decoder = DPTDecoderLite(
            num_ch_enc=self.encoder.num_ch_enc,  # 传入编码器通道数
            scales=range(3),                      # 与原始 DepthDecoder 保持一致
            use_booster=use_booster,
            use_acm=use_acm,
            use_s2conv=use_s2conv
        )

    def forward(self, input_image):
        # 获取编码器特征
        features = self.encoder(input_image)
        
        # 解码器现在直接返回包含多尺度输出的字典
        outputs = self.decoder(features)
        
        # 不需要额外处理，直接返回解码器输出
        return outputs

    def load_pretrained_weights(self, weights_folder):
        """加载预训练权重"""
        if weights_folder and os.path.isdir(weights_folder):
            # 加载编码器权重
            encoder_path = os.path.join(weights_folder, "encoder.pth")
            if os.path.exists(encoder_path):
                encoder_dict = torch.load(encoder_path)
                self.encoder.load_state_dict(encoder_dict)
                print("Loaded encoder weights")
            
            # 加载深度解码器权重
            depth_path = os.path.join(weights_folder, "depth.pth")
            if os.path.exists(depth_path):
                depth_dict = torch.load(depth_path)
                try:
                    # 尝试加载兼容的权重
                    self.decoder.load_state_dict(depth_dict, strict=False)
                    print("Loaded depth decoder weights")
                except Exception as e:
                    print(f"Partial loading of depth decoder weights: {str(e)}")
