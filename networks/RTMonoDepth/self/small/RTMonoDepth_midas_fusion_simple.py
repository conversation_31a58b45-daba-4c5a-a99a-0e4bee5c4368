"""
简化版MiDaS融合模型
确保与原始RT-MonoDepth完全兼容
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import os
from collections import OrderedDict

class DepthEncoder(nn.Module):
    """原始RT-MonoDepth编码器"""
    def __init__(self):
        super(DepthEncoder, self).__init__()
        self.num_ch_enc = [64, 64, 128, 192]
        self.num_ch_enc_build = [64, 64, 128, 192]
        self.convs = nn.ModuleList()

        self.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.relu = nn.ReLU(inplace=True)

        for l, (ch_in, ch_out) in enumerate(zip(self.num_ch_enc_build[:-1], self.num_ch_enc_build[1:])):
            layer = nn.Sequential(
                nn.Conv2d(ch_in, ch_out, kernel_size=3, stride=2, padding=1, bias=True),
                nn.LeakyReLU(0.1, inplace=True),
                nn.Conv2d(ch_out, ch_out, kernel_size=3, stride=1, padding=1, bias=True),
                nn.LeakyReLU(0.1, inplace=True)
            )
            self.convs.append(layer)

        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        features = []
        x = (x - 0.45) / 0.225
        x = self.conv1(x)
        x = self.relu(x)
        features.append(x)

        for conv in self.convs:
            x = conv(x)
            features.append(x)

        return features


class SimpleMiDaSFusion(nn.Module):
    """简化的MiDaS特征融合模块"""
    def __init__(self, input_channels):
        super(SimpleMiDaSFusion, self).__init__()
        
        self.input_channels = input_channels
        
        # 简单的特征增强模块
        self.feature_enhancers = nn.ModuleList()
        for ch in input_channels:
            self.feature_enhancers.append(
                nn.Sequential(
                    nn.Conv2d(ch, ch, 3, 1, 1, groups=ch),  # 深度可分离卷积
                    nn.Conv2d(ch, ch, 1),  # 点卷积
                    nn.BatchNorm2d(ch),
                    nn.ReLU6(inplace=True)
                )
            )
        
        # 跨尺度注意力（简化版）
        self.attention_weights = nn.ModuleList()
        for ch in input_channels:
            self.attention_weights.append(
                nn.Sequential(
                    nn.AdaptiveAvgPool2d(1),
                    nn.Conv2d(ch, ch // 4, 1),
                    nn.ReLU6(inplace=True),
                    nn.Conv2d(ch // 4, ch, 1),
                    nn.Sigmoid()
                )
            )
    
    def forward(self, features):
        """前向传播"""
        enhanced_features = []
        
        for i, (feat, enhancer, attention) in enumerate(zip(features, self.feature_enhancers, self.attention_weights)):
            # 特征增强
            enhanced = enhancer(feat)
            
            # 注意力加权
            weight = attention(enhanced)
            attended = enhanced * weight
            
            # 残差连接
            output = feat + attended
            enhanced_features.append(output)
        
        return enhanced_features


class SimpleDepthDecoder(nn.Module):
    """简化的深度解码器，与原始RT-MonoDepth兼容"""
    def __init__(self, num_ch_enc, scales=range(3), use_skips=True):
        super(SimpleDepthDecoder, self).__init__()

        self.use_skips = use_skips
        self.scales = scales
        self.num_ch_enc = num_ch_enc
        self.num_ch_dec = [16, 32, 64, 96]

        # 解码器
        self.convs = OrderedDict()
        for i in range(3, -1, -1):
            # upconv_0
            num_ch_in = self.num_ch_enc[-1] if i == 3 else self.num_ch_dec[i + 1]
            num_ch_out = self.num_ch_dec[i]
            
            self.convs[("upconv", i, 0)] = nn.Sequential(
                nn.Conv2d(num_ch_in, num_ch_out, 3, 1, 1),
                nn.BatchNorm2d(num_ch_out),
                nn.ReLU6(inplace=True)
            )

            # upconv_1
            num_ch_in = self.num_ch_dec[i]
            if self.use_skips and i > 0:
                num_ch_in += self.num_ch_enc[i - 1]
            
            self.convs[("upconv", i, 1)] = nn.Sequential(
                nn.Conv2d(num_ch_in, num_ch_out, 3, 1, 1),
                nn.BatchNorm2d(num_ch_out),
                nn.ReLU6(inplace=True)
            )

        # 深度预测头
        for s in self.scales:
            self.convs[("dispconv", s)] = nn.Sequential(
                nn.Conv2d(self.num_ch_dec[s], 32, 3, 1, 1),
                nn.ReLU6(inplace=True),
                nn.Conv2d(32, 1, 1),
                nn.Sigmoid()
            )

        self.decoder = nn.ModuleList(list(self.convs.values()))

    def forward(self, input_features):
        outputs = {}
        x = input_features[-1]
        
        for i in range(3, -1, -1):
            x = self.convs[("upconv", i, 0)](x)
            x = F.interpolate(x, scale_factor=2, mode="nearest")

            if self.use_skips and i > 0:
                x = torch.cat([x, input_features[i - 1]], 1)

            x = self.convs[("upconv", i, 1)](x)

            if i in self.scales:
                depth = self.convs[("dispconv", i)](x)
                outputs[("disp", i)] = depth

        return outputs


class RTMonoDepthMiDaSFusionSimple(nn.Module):
    """
    简化版RT-MonoDepth + MiDaS融合模型
    确保与原始架构完全兼容
    """
    def __init__(self, use_midas_fusion=True):
        super(RTMonoDepthMiDaSFusionSimple, self).__init__()
        
        self.encoder = DepthEncoder()
        
        if use_midas_fusion:
            # 使用简化的MiDaS特征融合
            self.midas_fusion = SimpleMiDaSFusion(self.encoder.num_ch_enc)
            self.decoder = SimpleDepthDecoder(self.encoder.num_ch_enc)
        else:
            # 不使用融合，直接使用原始解码器
            self.decoder = SimpleDepthDecoder(self.encoder.num_ch_enc)
            self.midas_fusion = None
    
    def forward(self, x):
        # 编码器特征提取
        encoder_features = self.encoder(x)
        
        # MiDaS特征融合（如果启用）
        if self.midas_fusion is not None:
            enhanced_features = self.midas_fusion(encoder_features)
            outputs = self.decoder(enhanced_features)
        else:
            outputs = self.decoder(encoder_features)
        
        return outputs


# Orin NX Super优化版本
class RTMonoDepthMiDaSFusionSimpleOptimized(RTMonoDepthMiDaSFusionSimple):
    """Orin NX Super优化版本"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._optimize_for_orin_nx_super()
    
    def _optimize_for_orin_nx_super(self):
        """Orin NX Super特定优化"""
        # 启用混合精度
        self.half()
        
        # 优化内存使用
        for module in self.modules():
            if isinstance(module, nn.BatchNorm2d):
                module.track_running_stats = False  # 减少内存使用
    
    def forward(self, x):
        # 确保输入是FP16
        if x.dtype != torch.float16:
            x = x.half()
        return super().forward(x)


def load_pretrained_weights(model, weights_folder):
    """
    加载预训练权重到MiDaS融合模型

    Args:
        model: MiDaS融合模型实例
        weights_folder: 权重文件夹路径

    Returns:
        tuple: (encoder_loaded, decoder_loaded) 加载的参数数量
    """
    encoder_path = os.path.join(weights_folder, "encoder.pth")
    depth_path = os.path.join(weights_folder, "depth.pth")

    if not os.path.exists(encoder_path) or not os.path.exists(depth_path):
        print(f"⚠️ 权重文件不存在: {encoder_path} 或 {depth_path}")
        return 0, 0

    # 加载原始权重
    encoder_dict = torch.load(encoder_path, map_location='cpu')
    depth_dict = torch.load(depth_path, map_location='cpu')

    # 获取模型状态字典
    model_state_dict = model.state_dict()

    # 1. 加载编码器权重
    encoder_loaded = 0
    for k, v in encoder_dict.items():
        if k not in ['height', 'width', 'use_stereo']:
            full_key = f"encoder.{k}"
            if full_key in model_state_dict:
                model_state_dict[full_key] = v
                encoder_loaded += 1

    # 2. 加载解码器权重（需要映射）
    decoder_loaded = 0

    # 创建权重映射表
    # 原始: decoder.0.conv.weight -> 新: decoder.decoder.0.0.weight
    # 原始: decoder.1.conv.weight -> 新: decoder.decoder.1.0.weight
    weight_mapping = {
        # upconv层映射
        'decoder.0.conv.weight': 'decoder.decoder.0.0.weight',
        'decoder.0.conv.bias': 'decoder.decoder.0.0.bias',
        'decoder.1.conv.weight': 'decoder.decoder.1.0.weight',
        'decoder.1.conv.bias': 'decoder.decoder.1.0.bias',
        'decoder.2.conv.weight': 'decoder.decoder.2.0.weight',
        'decoder.2.conv.bias': 'decoder.decoder.2.0.bias',
        'decoder.3.conv.weight': 'decoder.decoder.3.0.weight',
        'decoder.3.conv.bias': 'decoder.decoder.3.0.bias',
        'decoder.4.conv.weight': 'decoder.decoder.4.0.weight',
        'decoder.4.conv.bias': 'decoder.decoder.4.0.bias',
        'decoder.5.conv.weight': 'decoder.decoder.5.0.weight',
        'decoder.5.conv.bias': 'decoder.decoder.5.0.bias',
        'decoder.6.conv.weight': 'decoder.decoder.6.0.weight',
        'decoder.6.conv.bias': 'decoder.decoder.6.0.bias',
        'decoder.7.conv.weight': 'decoder.decoder.7.0.weight',
        'decoder.7.conv.bias': 'decoder.decoder.7.0.bias',

        # 深度预测头映射
        'decoder.8.conv3.weight': 'decoder.decoder.8.0.weight',
        'decoder.8.conv3.bias': 'decoder.decoder.8.0.bias',
        'decoder.9.conv3.weight': 'decoder.decoder.9.0.weight',
        'decoder.9.conv3.bias': 'decoder.decoder.9.0.bias',
        'decoder.10.conv3.weight': 'decoder.decoder.10.0.weight',
        'decoder.10.conv3.bias': 'decoder.decoder.10.0.bias',
    }

    # 应用权重映射
    for old_key, new_key in weight_mapping.items():
        if old_key in depth_dict and new_key in model_state_dict:
            old_weight = depth_dict[old_key]
            new_weight_shape = model_state_dict[new_key].shape

            # 检查形状是否匹配
            if old_weight.shape == new_weight_shape:
                model_state_dict[new_key] = old_weight
                decoder_loaded += 1
            else:
                print(f"⚠️ 形状不匹配 {old_key}: {old_weight.shape} -> {new_key}: {new_weight_shape}")

    # 应用所有权重到模型
    model.load_state_dict(model_state_dict, strict=False)

    return encoder_loaded, decoder_loaded


# 工厂函数
def create_rtmonodepth_midas_fusion_simple(pretrained_path=None, optimized_for_orin=True):
    """创建简化版RT-MonoDepth + MiDaS融合模型"""
    if optimized_for_orin:
        model = RTMonoDepthMiDaSFusionSimpleOptimized(use_midas_fusion=True)
    else:
        model = RTMonoDepthMiDaSFusionSimple(use_midas_fusion=True)

    if pretrained_path:
        if os.path.isdir(pretrained_path):
            # 如果是文件夹，加载RT-MonoDepth权重
            encoder_loaded, decoder_loaded = load_pretrained_weights(model, pretrained_path)
            print(f"Loaded pretrained weights: {encoder_loaded} encoder + {decoder_loaded} decoder")
        else:
            # 如果是文件，直接加载
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            model.load_state_dict(checkpoint, strict=False)
            print(f"Loaded pretrained weights from {pretrained_path}")

    return model


# 为了兼容性，提供原始名称的别名
RTMonoDepthMiDaSFusion = RTMonoDepthMiDaSFusionSimple
