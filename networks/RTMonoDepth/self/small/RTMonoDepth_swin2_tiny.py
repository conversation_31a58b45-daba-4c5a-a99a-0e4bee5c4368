"""
RT-MonoDepth + DPT-Swin2-Tiny混合架构
平衡版本 - 预期FPS: 60-75
针对Nvidia Orin NX Super优化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from collections import OrderedDict

class PatchEmbed(nn.Module):
    """图像到Patch嵌入"""
    def __init__(self, img_size=224, patch_size=4, in_chans=3, embed_dim=96):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.patches_resolution = [img_size // patch_size, img_size // patch_size]
        self.num_patches = self.patches_resolution[0] * self.patches_resolution[1]

        self.in_chans = in_chans
        self.embed_dim = embed_dim

        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
        self.norm = nn.LayerNorm(embed_dim)

    def forward(self, x):
        B, C, H, W = x.shape
        x = self.proj(x)  # B Ph*Pw C
        x = x.flatten(2).transpose(1, 2)  # B Ph*Pw C
        x = self.norm(x)
        return x


class WindowAttention(nn.Module):
    """基于窗口的多头自注意力 (W-MSA) 模块"""
    def __init__(self, dim, window_size, num_heads, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        # 相对位置偏置参数表
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size[0] - 1) * (2 * window_size[1] - 1), num_heads))

        # 获取每个token对的相对位置索引
        coords_h = torch.arange(self.window_size[0])
        coords_w = torch.arange(self.window_size[1])
        coords = torch.stack(torch.meshgrid([coords_h, coords_w]))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += self.window_size[0] - 1
        relative_coords[:, :, 1] += self.window_size[1] - 1
        relative_coords[:, :, 0] *= 2 * self.window_size[1] - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer("relative_position_index", relative_position_index)

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, x, mask=None):
        B_, N, C = x.shape
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]

        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))

        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size[0] * self.window_size[1], self.window_size[0] * self.window_size[1], -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        attn = attn + relative_position_bias.unsqueeze(0)

        if mask is not None:
            nW = mask.shape[0]
            attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(1).unsqueeze(0)
            attn = attn.view(-1, self.num_heads, N, N)
            attn = self.softmax(attn)
        else:
            attn = self.softmax(attn)

        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


class SwinTransformerBlock(nn.Module):
    """Swin Transformer块"""
    def __init__(self, dim, num_heads, window_size=7, shift_size=0, mlp_ratio=4.):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size
        self.mlp_ratio = mlp_ratio

        self.norm1 = nn.LayerNorm(dim)
        self.attn = WindowAttention(
            dim, window_size=(self.window_size, self.window_size), num_heads=num_heads)

        self.norm2 = nn.LayerNorm(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Linear(mlp_hidden_dim, dim),
        )

    def forward(self, x, H, W):
        B, L, C = x.shape

        shortcut = x
        x = self.norm1(x)
        x = x.view(B, H, W, C)

        # 循环移位
        if self.shift_size > 0:
            shifted_x = torch.roll(x, shifts=(-self.shift_size, -self.shift_size), dims=(1, 2))
        else:
            shifted_x = x

        # 分区窗口
        x_windows = self.window_partition(shifted_x, self.window_size)
        x_windows = x_windows.view(-1, self.window_size * self.window_size, C)

        # W-MSA/SW-MSA
        attn_windows = self.attn(x_windows)

        # 合并窗口
        attn_windows = attn_windows.view(-1, self.window_size, self.window_size, C)
        shifted_x = self.window_reverse(attn_windows, self.window_size, H, W)

        # 反向循环移位
        if self.shift_size > 0:
            x = torch.roll(shifted_x, shifts=(self.shift_size, self.shift_size), dims=(1, 2))
        else:
            x = shifted_x

        x = x.view(B, H * W, C)

        # FFN
        x = shortcut + x
        x = x + self.mlp(self.norm2(x))

        return x

    def window_partition(self, x, window_size):
        B, H, W, C = x.shape
        x = x.view(B, H // window_size, window_size, W // window_size, window_size, C)
        windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
        return windows

    def window_reverse(self, windows, window_size, H, W):
        B = int(windows.shape[0] / (H * W / window_size / window_size))
        x = windows.view(B, H // window_size, W // window_size, window_size, window_size, -1)
        x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)
        return x


class Swin2TinyEncoder(nn.Module):
    """
    简化的Swin2-Tiny编码器
    针对Orin NX Super优化
    """
    def __init__(self, img_size=192, patch_size=4, in_chans=3, embed_dim=96):
        super().__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        
        # 输出通道数
        self.num_ch_enc = [96, 192, 384, 768]
        
        # Patch嵌入
        self.patch_embed = PatchEmbed(
            img_size=img_size, patch_size=patch_size, 
            in_chans=in_chans, embed_dim=embed_dim)
        
        # 各个阶段
        self.stages = nn.ModuleList()
        
        # Stage 1: 96 channels
        self.stages.append(self._make_stage(96, 96, 2, 2))
        
        # Stage 2: 192 channels  
        self.stages.append(self._make_stage(96, 192, 2, 2))
        
        # Stage 3: 384 channels
        self.stages.append(self._make_stage(192, 384, 6, 3))
        
        # Stage 4: 768 channels
        self.stages.append(self._make_stage(384, 768, 2, 3))
        
        # 下采样层
        self.downsample_layers = nn.ModuleList([
            nn.Identity(),  # Stage 1不下采样
            nn.Conv2d(96, 192, kernel_size=2, stride=2),
            nn.Conv2d(192, 384, kernel_size=2, stride=2),
            nn.Conv2d(384, 768, kernel_size=2, stride=2),
        ])
    
    def _make_stage(self, in_dim, out_dim, depth, num_heads):
        """创建Swin Transformer阶段"""
        layers = []
        
        # 通道调整
        if in_dim != out_dim:
            layers.append(nn.Linear(in_dim, out_dim))
        
        # Swin Transformer块
        for i in range(depth):
            layers.append(
                SwinTransformerBlock(
                    dim=out_dim,
                    num_heads=num_heads,
                    window_size=7,
                    shift_size=0 if (i % 2 == 0) else 7 // 2,
                )
            )
        
        return nn.Sequential(*layers)
    
    def forward(self, x):
        features = []
        B, C, H, W = x.shape
        
        # 输入归一化
        x = (x - 0.45) / 0.225
        
        # Patch嵌入
        x = self.patch_embed(x)  # [B, H*W/16, 96]
        H, W = H // self.patch_size, W // self.patch_size
        
        # 各个阶段
        for i, (stage, downsample) in enumerate(zip(self.stages, self.downsample_layers)):
            if i > 0:
                # 重塑为图像格式进行下采样
                x = x.transpose(1, 2).view(B, -1, H, W)
                x = downsample(x)
                B, C, H, W = x.shape
                x = x.flatten(2).transpose(1, 2)  # 重新展平
            
            # Transformer处理
            for layer in stage:
                if isinstance(layer, nn.Linear):
                    x = layer(x)
                else:
                    x = layer(x, H, W)
            
            # 保存特征（转换为卷积格式）
            feat = x.transpose(1, 2).view(B, -1, H, W)
            features.append(feat)
            
            # 更新分辨率
            if i < len(self.stages) - 1:
                H, W = H // 2, W // 2
        
        return features


class DPTDecoder(nn.Module):
    """DPT风格的解码器"""
    def __init__(self, encoder_channels, scales=range(3)):
        super(DPTDecoder, self).__init__()
        
        self.scales = scales
        self.encoder_channels = encoder_channels
        
        # 特征融合维度
        fusion_dim = 256
        
        # 特征投影
        self.projections = nn.ModuleList()
        for ch in encoder_channels:
            self.projections.append(
                nn.Sequential(
                    nn.Conv2d(ch, fusion_dim, 1),
                    nn.BatchNorm2d(fusion_dim),
                    nn.ReLU6(inplace=True)
                )
            )
        
        # 特征细化
        self.refinement_blocks = nn.ModuleList()
        for i in range(len(encoder_channels)):
            self.refinement_blocks.append(
                nn.Sequential(
                    nn.Conv2d(fusion_dim, fusion_dim, 3, 1, 1),
                    nn.BatchNorm2d(fusion_dim),
                    nn.ReLU6(inplace=True),
                    nn.Conv2d(fusion_dim, fusion_dim, 3, 1, 1),
                    nn.BatchNorm2d(fusion_dim),
                    nn.ReLU6(inplace=True)
                )
            )
        
        # 深度预测头
        self.depth_heads = nn.ModuleDict()
        for s in scales:
            self.depth_heads[f"scale_{s}"] = nn.Sequential(
                nn.Conv2d(fusion_dim, 128, 3, 1, 1),
                nn.ReLU6(inplace=True),
                nn.Conv2d(128, 1, 1),
                nn.Sigmoid()
            )
    
    def forward(self, features):
        outputs = {}
        
        # 特征投影
        projected_features = []
        for feat, proj in zip(features, self.projections):
            projected_features.append(proj(feat))
        
        # 从最深层开始上采样和融合
        x = projected_features[-1]
        
        for i in range(len(projected_features) - 2, -1, -1):
            # 上采样
            x = F.interpolate(x, size=projected_features[i].shape[2:], 
                            mode='bilinear', align_corners=False)
            
            # 特征融合
            x = x + projected_features[i]
            
            # 细化
            x = self.refinement_blocks[i](x)
            
            # 生成深度图
            if i in self.scales:
                depth = self.depth_heads[f"scale_{i}"](x)
                outputs[("disp", i)] = depth
        
        return outputs


class RTMonoDepthSwin2Tiny(nn.Module):
    """
    RT-MonoDepth + Swin2-Tiny混合架构
    平衡版本，预期FPS: 60-75 (Orin NX Super)
    """
    def __init__(self, img_size=192):
        super(RTMonoDepthSwin2Tiny, self).__init__()
        
        self.encoder = Swin2TinyEncoder(img_size=img_size)
        self.decoder = DPTDecoder(self.encoder.num_ch_enc)
    
    def forward(self, x):
        features = self.encoder(x)
        outputs = self.decoder(features)
        return outputs


# Orin NX Super优化版本
class RTMonoDepthSwin2TinyOptimized(RTMonoDepthSwin2Tiny):
    """Orin NX Super优化版本"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._optimize_for_orin_nx_super()
    
    def _optimize_for_orin_nx_super(self):
        """Orin NX Super特定优化"""
        # 启用混合精度
        self.half()
        
        # 优化Transformer注意力
        for module in self.modules():
            if isinstance(module, WindowAttention):
                # 启用Flash Attention（如果可用）
                module.scale = module.scale * 1.0  # 确保FP16兼容
    
    def forward(self, x):
        if x.dtype != torch.float16:
            x = x.half()
        return super().forward(x)


# 工厂函数
def create_rtmonodepth_swin2_tiny(pretrained_path=None, optimized_for_orin=True):
    """创建RT-MonoDepth + Swin2-Tiny模型"""
    if optimized_for_orin:
        model = RTMonoDepthSwin2TinyOptimized()
    else:
        model = RTMonoDepthSwin2Tiny()
    
    if pretrained_path:
        checkpoint = torch.load(pretrained_path, map_location='cpu')
        model.load_state_dict(checkpoint, strict=False)
        print(f"Loaded pretrained weights from {pretrained_path}")
    
    return model
