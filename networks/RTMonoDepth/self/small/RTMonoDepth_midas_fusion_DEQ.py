"""
RT-MonoDepth + MiDaS融合 + DualRefine DEQ后处理模型
集成Deep Equilibrium Networks进行深度细化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import os
import numpy as np
from collections import OrderedDict

# 导入基础MiDaS融合模型
from .RTMonoDepth_midas_fusion_simple import (
    DepthEncoder, 
    SimpleMiDaSFusion, 
    SimpleDepthDecoder,
    load_pretrained_weights
)

# 简化的DEQ实现，不依赖外部库
DEQ_AVAILABLE = True  # 总是可用，使用我们自己的实现

def anderson_acceleration(f, x0, m=5, max_iter=50, tol=1e-4, beta=1.0):
    """
    简化的Anderson加速算法实现
    基于DualRefine的思想，但不依赖外部库
    """
    x = x0.clone()
    f_x = f(x)

    # 存储历史信息
    X = [x.clone()]
    F = [f_x.clone()]

    for k in range(max_iter):
        # 计算残差
        residual = f_x - x

        # 检查收敛
        if torch.norm(residual) < tol:
            break

        # Anderson加速
        if k > 0 and len(X) > 1:
            # 构建矩阵
            m_k = min(m, len(X) - 1)

            # 计算差分
            dF = torch.stack([F[i] - F[i-1] for i in range(len(F)-m_k, len(F))], dim=0)
            dX = torch.stack([X[i] - X[i-1] for i in range(len(X)-m_k, len(X))], dim=0)

            # 重塑为矩阵形式
            dF_flat = dF.view(dF.shape[0], -1).T
            dX_flat = dX.view(dX.shape[0], -1).T

            try:
                # 求解最小二乘问题
                alpha = torch.linalg.lstsq(dF_flat, (f_x - x).view(-1)).solution

                # 更新
                x_new = x.clone()
                f_new = f_x.clone()

                for i, a in enumerate(alpha):
                    idx = len(X) - m_k + i
                    x_new = x_new - a * (X[idx] - X[idx-1])
                    f_new = f_new - a * (F[idx] - F[idx-1])

                x = beta * x_new + (1 - beta) * x
                f_x = f(x)

            except:
                # 如果求解失败，使用简单更新
                x = beta * f_x + (1 - beta) * x
                f_x = f(x)
        else:
            # 简单的固定点迭代
            x = beta * f_x + (1 - beta) * x
            f_x = f(x)

        # 存储历史
        X.append(x.clone())
        F.append(f_x.clone())

        # 限制历史长度
        if len(X) > m + 1:
            X.pop(0)
            F.pop(0)

    return x, k + 1


class DEQRefinementModule(nn.Module):
    """
    Deep Equilibrium Networks深度细化模块
    基于DualRefine的DEQ实现
    """
    def __init__(self, input_channels=1, hidden_dim=64, num_iterations=5):
        super(DEQRefinementModule, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_iterations = num_iterations
        self.input_channels = input_channels
        
        # DEQ求解器配置
        self.use_anderson = True  # 使用Anderson加速
        
        # 训练和评估的收敛阈值
        self.train_f_thres = 1e-3
        self.eval_f_thres = 1e-4
        
        # 特征提取网络
        self.feature_extractor = nn.Sequential(
            nn.Conv2d(input_channels, hidden_dim//2, 3, 1, 1),
            nn.BatchNorm2d(hidden_dim//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim//2, hidden_dim, 3, 1, 1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True)
        )
        
        # DEQ函数网络 - 用于迭代细化
        self.deq_function = nn.Sequential(
            nn.Conv2d(hidden_dim + input_channels, hidden_dim, 3, 1, 1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim, hidden_dim, 3, 1, 1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim, hidden_dim, 3, 1, 1)
        )
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim//2, 3, 1, 1),
            nn.BatchNorm2d(hidden_dim//2),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim//2, input_channels, 1),
            nn.Sigmoid()
        )
        
        # 残差连接权重
        self.residual_weight = nn.Parameter(torch.tensor(0.1))
    
    def deq_func(self, z, depth_input):
        """DEQ固定点函数"""
        # 将隐藏状态和输入深度图拼接
        combined = torch.cat([z, depth_input], dim=1)
        
        # 通过DEQ函数网络
        delta_z = self.deq_function(combined)
        
        # 返回新的隐藏状态
        return delta_z
    
    def _fixed_point_solve(self, deq_func, z_init, depth_input, threshold=None):
        """固定点求解"""
        if threshold is None:
            threshold = self.train_f_thres if self.training else self.eval_f_thres

        # 定义包装函数
        def wrapped_func(z):
            return deq_func(z)

        if self.use_anderson:
            try:
                # 使用Anderson加速求解
                z_star, iterations = anderson_acceleration(
                    wrapped_func,
                    z_init,
                    max_iter=self.num_iterations,
                    tol=threshold
                )
                return z_star, [z_star], 0.0, 0.0
            except Exception as e:
                print(f"Anderson acceleration failed: {e}, falling back to simple iteration")
                return self._simple_iteration(deq_func, z_init, depth_input)
        else:
            return self._simple_iteration(deq_func, z_init, depth_input)
    
    def _simple_iteration(self, deq_func, z_init, depth_input):
        """简单迭代求解（备用方案）"""
        z = z_init
        trajectory = [z]

        for i in range(self.num_iterations):
            z_new = deq_func(z)  # deq_func已经是闭包，不需要传递depth_input
            trajectory.append(z_new)

            # 检查收敛
            if i > 0:
                diff = torch.norm(z_new - z)
                if diff < self.train_f_thres:
                    break
            z = z_new

        return z, trajectory, 0.0, 0.0
    
    def forward(self, depth_input):
        """前向传播"""
        B, C, H, W = depth_input.shape
        
        # 提取初始特征
        initial_features = self.feature_extractor(depth_input)
        
        # 定义DEQ函数
        def deq_function_wrapper(z):
            return self.deq_func(z, depth_input)
        
        # 创建闭包函数，捕获depth_input
        def deq_func_closure(z):
            return self.deq_func(z, depth_input)

        # 固定点求解
        z_star, trajectory, rel_error, abs_error = self._fixed_point_solve(
            deq_func_closure,
            initial_features,
            depth_input
        )
        
        # 生成细化的深度图
        refined_depth = self.output_projection(z_star)
        
        # 残差连接
        output_depth = depth_input + self.residual_weight * (refined_depth - depth_input)
        
        # 确保输出在合理范围内
        output_depth = torch.clamp(output_depth, 0.0, 1.0)
        
        return output_depth


class RTMonoDepthMiDaSFusionDEQ(nn.Module):
    """
    RT-MonoDepth + MiDaS融合 + DEQ后处理完整模型
    """
    def __init__(self, use_midas_fusion=True, use_deq_refinement=True, deq_hidden_dim=64):
        super(RTMonoDepthMiDaSFusionDEQ, self).__init__()
        
        self.use_deq_refinement = use_deq_refinement and DEQ_AVAILABLE
        
        # 基础编码器
        self.encoder = DepthEncoder()
        
        # MiDaS特征融合模块
        if use_midas_fusion:
            self.midas_fusion = SimpleMiDaSFusion(self.encoder.num_ch_enc)
            self.decoder = SimpleDepthDecoder(self.encoder.num_ch_enc)
        else:
            self.decoder = SimpleDepthDecoder(self.encoder.num_ch_enc)
            self.midas_fusion = None
        
        # DEQ细化模块
        if self.use_deq_refinement:
            self.deq_refinement = DEQRefinementModule(
                input_channels=1, 
                hidden_dim=deq_hidden_dim
            )
            print("-> DEQ refinement module enabled")
        else:
            self.deq_refinement = None
            if not DEQ_AVAILABLE:
                print("-> DEQ refinement disabled (modules not available)")
            else:
                print("-> DEQ refinement disabled by user")
    
    def forward(self, x):
        """前向传播"""
        # 1. 编码器特征提取
        encoder_features = self.encoder(x)
        
        # 2. MiDaS特征融合（如果启用）
        if self.midas_fusion is not None:
            enhanced_features = self.midas_fusion(encoder_features)
            outputs = self.decoder(enhanced_features)
        else:
            outputs = self.decoder(encoder_features)
        
        # 3. DEQ后处理细化（如果启用）
        if self.use_deq_refinement and self.deq_refinement is not None:
            # 对主要输出进行DEQ细化
            main_depth = outputs[("disp", 0)]
            refined_depth = self.deq_refinement(main_depth)
            
            # 更新输出
            outputs[("disp", 0)] = refined_depth
            outputs[("disp_refined", 0)] = refined_depth
            outputs[("disp_original", 0)] = main_depth
        
        return outputs


# Orin NX Super优化版本
class RTMonoDepthMiDaSFusionDEQOptimized(RTMonoDepthMiDaSFusionDEQ):
    """Orin NX Super优化版本"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._optimize_for_orin_nx_super()
    
    def _optimize_for_orin_nx_super(self):
        """Orin NX Super特定优化"""
        # 启用混合精度
        self.half()
        
        # 优化DEQ求解器参数
        if self.deq_refinement is not None:
            self.deq_refinement.train_f_thres = 5e-3  # 放宽训练收敛阈值
            self.deq_refinement.eval_f_thres = 1e-3   # 放宽评估收敛阈值
            self.deq_refinement.num_iterations = 3    # 减少迭代次数
        
        # 优化内存使用
        for module in self.modules():
            if isinstance(module, nn.BatchNorm2d):
                module.track_running_stats = False
    
    def forward(self, x):
        # 确保输入是FP16
        if x.dtype != torch.float16:
            x = x.half()
        return super().forward(x)


def load_pretrained_weights_deq(model, weights_folder):
    """
    加载预训练权重到DEQ模型
    """
    # 使用基础的权重加载函数
    encoder_loaded, decoder_loaded = load_pretrained_weights(model, weights_folder)
    
    # DEQ模块从零开始训练
    deq_initialized = 0
    if hasattr(model, 'deq_refinement') and model.deq_refinement is not None:
        # 可以在这里添加DEQ模块的特殊初始化
        print("-> DEQ refinement module initialized from scratch")
        deq_initialized = sum(p.numel() for p in model.deq_refinement.parameters())
    
    return encoder_loaded, decoder_loaded, deq_initialized


# 工厂函数
def create_rtmonodepth_midas_fusion_deq(pretrained_path=None, optimized_for_orin=True, 
                                       use_deq=True, deq_hidden_dim=64):
    """创建RT-MonoDepth + MiDaS融合 + DEQ模型"""
    if optimized_for_orin:
        model = RTMonoDepthMiDaSFusionDEQOptimized(
            use_midas_fusion=True, 
            use_deq_refinement=use_deq,
            deq_hidden_dim=deq_hidden_dim
        )
    else:
        model = RTMonoDepthMiDaSFusionDEQ(
            use_midas_fusion=True, 
            use_deq_refinement=use_deq,
            deq_hidden_dim=deq_hidden_dim
        )
    
    if pretrained_path:
        if os.path.isdir(pretrained_path):
            # 如果是文件夹，加载RT-MonoDepth权重
            encoder_loaded, decoder_loaded, deq_initialized = load_pretrained_weights_deq(model, pretrained_path)
            print(f"Loaded pretrained weights: {encoder_loaded} encoder + {decoder_loaded} decoder + {deq_initialized} DEQ")
        else:
            # 如果是文件，直接加载
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            model.load_state_dict(checkpoint, strict=False)
            print(f"Loaded pretrained weights from {pretrained_path}")
    
    return model


# 为了兼容性，提供别名
RTMonoDepthMiDaSFusionDEQ = RTMonoDepthMiDaSFusionDEQ
