"""
RT-MonoDepth + DPT-LeViT-224混合架构
高精度版本 - 预期FPS: 50-65
针对Nvidia Orin NX Super优化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from collections import OrderedDict

class ConvNorm(nn.Module):
    """卷积 + 归一化层"""
    def __init__(self, in_channels, out_channels, kernel_size=1, stride=1, padding=0, dilation=1, groups=1, bn_weight_init=1):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding, dilation, groups, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        nn.init.constant_(self.bn.weight, bn_weight_init)
        nn.init.constant_(self.bn.bias, 0)

    def forward(self, x):
        return self.bn(self.conv(x))


class LinearNorm(nn.Module):
    """线性层 + 归一化"""
    def __init__(self, in_features, out_features, bn_weight_init=1):
        super().__init__()
        self.linear = nn.Linear(in_features, out_features, bias=False)
        self.bn = nn.BatchNorm1d(out_features)
        nn.init.constant_(self.bn.weight, bn_weight_init)
        nn.init.constant_(self.bn.bias, 0)

    def forward(self, x):
        x = self.linear(x)
        return self.bn(x.flatten(0, 1)).reshape_as(x)


class Attention(nn.Module):
    """LeViT注意力模块"""
    def __init__(self, dim, key_dim, num_heads=8, attn_ratio=4, resolution=14):
        super().__init__()
        self.num_heads = num_heads
        self.scale = key_dim ** -0.5
        self.key_dim = key_dim
        self.nh_kd = nh_kd = key_dim * num_heads
        self.d = int(attn_ratio * key_dim)
        self.dh = int(attn_ratio * key_dim) * num_heads
        self.attn_ratio = attn_ratio
        h = self.dh + nh_kd * 2
        
        self.qkv = LinearNorm(dim, h)
        self.proj = LinearNorm(self.dh, dim, bn_weight_init=0)

        # 位置编码
        points = list(range(resolution))
        N = len(points)
        attention_offsets = {}
        idxs = []
        for p1 in points:
            for p2 in points:
                offset = abs(p1 - p2)
                if offset not in attention_offsets:
                    attention_offsets[offset] = len(attention_offsets)
                idxs.append(attention_offsets[offset])
        
        self.attention_biases = nn.Parameter(torch.zeros(num_heads, len(attention_offsets)))
        self.register_buffer('attention_bias_idxs', torch.LongTensor(idxs).view(N, N))

    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x)
        q, k, v = qkv.view(B, N, self.num_heads, -1).split([self.key_dim, self.key_dim, self.d], dim=3)
        q = q.permute(0, 2, 1, 3)
        k = k.permute(0, 2, 1, 3)
        v = v.permute(0, 2, 1, 3)

        attn = (q @ k.transpose(-2, -1)) * self.scale + self.attention_biases[:, self.attention_bias_idxs]
        attn = attn.softmax(dim=-1)

        x = (attn @ v).transpose(1, 2).reshape(B, N, self.dh)
        x = self.proj(x)
        return x


class Subsample(nn.Module):
    """下采样模块"""
    def __init__(self, stride, resolution):
        super().__init__()
        self.stride = stride
        self.resolution = resolution

    def forward(self, x):
        B, N, C = x.shape
        x = x.view(B, self.resolution, self.resolution, C)
        x = x[:, ::self.stride, ::self.stride].reshape(B, -1, C)
        return x


class LeViTBlock(nn.Module):
    """LeViT Transformer块"""
    def __init__(self, dim, key_dim, num_heads, mlp_ratio=4, attn_ratio=2, resolution=14):
        super().__init__()
        self.norm1 = nn.LayerNorm(dim)
        self.attn = Attention(dim, key_dim, num_heads, attn_ratio, resolution)
        
        self.norm2 = nn.LayerNorm(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = nn.Sequential(
            LinearNorm(dim, mlp_hidden_dim),
            nn.Hardswish(),  # 对移动设备友好
            LinearNorm(mlp_hidden_dim, dim, bn_weight_init=0)
        )

    def forward(self, x):
        x = x + self.attn(self.norm1(x))
        x = x + self.mlp(self.norm2(x))
        return x


class LeViTEncoder(nn.Module):
    """
    简化的LeViT编码器
    针对Orin NX Super优化
    """
    def __init__(self, img_size=224, patch_size=16, in_chans=3, embed_dims=[128, 256, 384]):
        super().__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.embed_dims = embed_dims
        
        # 输出通道数
        self.num_ch_enc = [128, 256, 384, 512]
        
        # Patch嵌入
        self.patch_embed = ConvNorm(in_chans, embed_dims[0] // 8, 3, 2, 1)
        
        # 各个阶段
        self.stages = nn.ModuleList()
        
        # Stage 1: 128 dim
        resolution = img_size // 4
        self.stages.append(self._make_stage(embed_dims[0] // 8, embed_dims[0], 2, 16, 4, resolution))
        
        # Stage 2: 256 dim
        resolution = resolution // 2
        self.stages.append(self._make_stage(embed_dims[0], embed_dims[1], 3, 16, 4, resolution))
        
        # Stage 3: 384 dim
        resolution = resolution // 2
        self.stages.append(self._make_stage(embed_dims[1], embed_dims[2], 4, 16, 4, resolution))
        
        # Stage 4: 512 dim (额外的特征提取)
        resolution = resolution // 2
        self.stages.append(self._make_stage(embed_dims[2], 512, 2, 16, 4, resolution))
        
        # 下采样层
        self.downsample_layers = nn.ModuleList([
            nn.Sequential(
                ConvNorm(embed_dims[0] // 8, embed_dims[0], 3, 2, 1),
                nn.Hardswish()
            ),
            nn.Sequential(
                ConvNorm(embed_dims[0], embed_dims[1], 3, 2, 1),
                nn.Hardswish()
            ),
            nn.Sequential(
                ConvNorm(embed_dims[1], embed_dims[2], 3, 2, 1),
                nn.Hardswish()
            ),
            nn.Sequential(
                ConvNorm(embed_dims[2], 512, 3, 2, 1),
                nn.Hardswish()
            ),
        ])
    
    def _make_stage(self, in_dim, out_dim, depth, key_dim, num_heads, resolution):
        """创建LeViT阶段"""
        layers = []
        
        # 通道调整
        if in_dim != out_dim:
            layers.append(LinearNorm(in_dim, out_dim))
        
        # LeViT块
        for i in range(depth):
            layers.append(
                LeViTBlock(
                    dim=out_dim,
                    key_dim=key_dim,
                    num_heads=num_heads,
                    resolution=resolution
                )
            )
        
        return nn.Sequential(*layers)
    
    def forward(self, x):
        features = []
        B, C, H, W = x.shape
        
        # 输入归一化
        x = (x - 0.45) / 0.225
        
        # 初始卷积
        x = self.patch_embed(x)  # [B, dim//8, H/2, W/2]
        
        # 各个阶段
        for i, (stage, downsample) in enumerate(zip(self.stages, self.downsample_layers)):
            # 下采样
            x = downsample(x)
            B, C, H, W = x.shape
            
            # 转换为序列格式
            x_seq = x.flatten(2).transpose(1, 2)  # [B, H*W, C]
            
            # Transformer处理
            for layer in stage:
                if isinstance(layer, LinearNorm):
                    x_seq = layer(x_seq)
                else:
                    x_seq = layer(x_seq)
            
            # 转换回卷积格式
            x = x_seq.transpose(1, 2).view(B, -1, H, W)
            features.append(x)
        
        return features


class EnhancedDPTDecoder(nn.Module):
    """增强的DPT解码器，针对LeViT优化"""
    def __init__(self, encoder_channels, scales=range(3)):
        super(EnhancedDPTDecoder, self).__init__()
        
        self.scales = scales
        self.encoder_channels = encoder_channels
        
        # 特征融合维度
        fusion_dim = 256
        
        # 特征投影（带注意力）
        self.projections = nn.ModuleList()
        for ch in encoder_channels:
            self.projections.append(
                nn.Sequential(
                    nn.Conv2d(ch, fusion_dim, 1),
                    nn.BatchNorm2d(fusion_dim),
                    nn.Hardswish(),
                    # 通道注意力
                    nn.AdaptiveAvgPool2d(1),
                    nn.Conv2d(fusion_dim, fusion_dim // 16, 1),
                    nn.Hardswish(),
                    nn.Conv2d(fusion_dim // 16, fusion_dim, 1),
                    nn.Sigmoid()
                )
            )
        
        # 特征细化（使用深度可分离卷积）
        self.refinement_blocks = nn.ModuleList()
        for i in range(len(encoder_channels)):
            self.refinement_blocks.append(
                nn.Sequential(
                    # 深度可分离卷积
                    nn.Conv2d(fusion_dim, fusion_dim, 3, 1, 1, groups=fusion_dim),
                    nn.Conv2d(fusion_dim, fusion_dim, 1),
                    nn.BatchNorm2d(fusion_dim),
                    nn.Hardswish(),
                    nn.Conv2d(fusion_dim, fusion_dim, 3, 1, 1, groups=fusion_dim),
                    nn.Conv2d(fusion_dim, fusion_dim, 1),
                    nn.BatchNorm2d(fusion_dim),
                    nn.Hardswish()
                )
            )
        
        # 多尺度融合
        self.multi_scale_fusion = nn.ModuleList()
        for i in range(len(encoder_channels) - 1):
            self.multi_scale_fusion.append(
                nn.Sequential(
                    nn.Conv2d(fusion_dim * 2, fusion_dim, 1),
                    nn.BatchNorm2d(fusion_dim),
                    nn.Hardswish()
                )
            )
        
        # 深度预测头（增强版）
        self.depth_heads = nn.ModuleDict()
        for s in scales:
            self.depth_heads[f"scale_{s}"] = nn.Sequential(
                nn.Conv2d(fusion_dim, 128, 3, 1, 1),
                nn.BatchNorm2d(128),
                nn.Hardswish(),
                nn.Conv2d(128, 64, 3, 1, 1),
                nn.BatchNorm2d(64),
                nn.Hardswish(),
                nn.Conv2d(64, 1, 1),
                nn.Sigmoid()
            )
    
    def forward(self, features):
        outputs = {}
        
        # 特征投影和注意力
        projected_features = []
        for feat, proj in zip(features, self.projections):
            # 分离投影和注意力
            proj_layers = list(proj.children())
            projected = nn.Sequential(*proj_layers[:3])(feat)  # 投影部分
            attention = nn.Sequential(*proj_layers[3:])(projected)  # 注意力部分
            attended_feat = projected * attention
            projected_features.append(attended_feat)
        
        # 从最深层开始上采样和融合
        x = projected_features[-1]
        
        for i in range(len(projected_features) - 2, -1, -1):
            # 上采样
            x = F.interpolate(x, size=projected_features[i].shape[2:], 
                            mode='bilinear', align_corners=False)
            
            # 多尺度特征融合
            fused = torch.cat([x, projected_features[i]], dim=1)
            x = self.multi_scale_fusion[i](fused)
            
            # 特征细化
            x = self.refinement_blocks[i](x)
            
            # 生成深度图
            if i in self.scales:
                depth = self.depth_heads[f"scale_{i}"](x)
                outputs[("disp", i)] = depth
        
        return outputs


class RTMonoDepthLeViT224(nn.Module):
    """
    RT-MonoDepth + LeViT-224混合架构
    高精度版本，预期FPS: 50-65 (Orin NX Super)
    """
    def __init__(self, img_size=224):
        super(RTMonoDepthLeViT224, self).__init__()
        
        self.encoder = LeViTEncoder(img_size=img_size)
        self.decoder = EnhancedDPTDecoder(self.encoder.num_ch_enc)
    
    def forward(self, x):
        # 调整输入尺寸到224x224（如果需要）
        if x.shape[2:] != (224, 224):
            x = F.interpolate(x, size=(224, 224), mode='bilinear', align_corners=False)
        
        features = self.encoder(x)
        outputs = self.decoder(features)
        return outputs


# Orin NX Super优化版本
class RTMonoDepthLeViT224Optimized(RTMonoDepthLeViT224):
    """Orin NX Super优化版本"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._optimize_for_orin_nx_super()
    
    def _optimize_for_orin_nx_super(self):
        """Orin NX Super特定优化"""
        # 启用混合精度
        self.half()
        
        # 优化BatchNorm
        for module in self.modules():
            if isinstance(module, nn.BatchNorm2d):
                module.eps = 1e-4  # 提高数值稳定性
                module.momentum = 0.03  # 加快收敛
    
    def forward(self, x):
        if x.dtype != torch.float16:
            x = x.half()
        return super().forward(x)


# 工厂函数
def create_rtmonodepth_levit_224(pretrained_path=None, optimized_for_orin=True):
    """创建RT-MonoDepth + LeViT-224模型"""
    if optimized_for_orin:
        model = RTMonoDepthLeViT224Optimized()
    else:
        model = RTMonoDepthLeViT224()
    
    if pretrained_path:
        checkpoint = torch.load(pretrained_path, map_location='cpu')
        model.load_state_dict(checkpoint, strict=False)
        print(f"Loaded pretrained weights from {pretrained_path}")
    
    return model
