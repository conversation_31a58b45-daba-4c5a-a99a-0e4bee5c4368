"""
RT-MonoDepth模型工厂
统一管理三种针对Orin NX Super优化的模型
"""

import torch
import torch.nn as nn
from .RTMonoDepth_midas_fusion import create_rtmonodepth_midas_fusion
from .RTMonoDepth_swin2_tiny import create_rtmonodepth_swin2_tiny
from .RTMonoDepth_levit_224 import create_rtmonodepth_levit_224

# 模型配置
MODEL_CONFIGS = {
    'midas_fusion': {
        'name': 'RT-MonoDepth + MiDaS特征融合',
        'description': '高性能版本',
        'expected_fps': '70-85',
        'parameters': '31M',
        'accuracy_improvement': '+15-20%',
        'memory_usage': '<1.5GB',
        'best_for': '实时应用，高帧率要求'
    },
    'swin2_tiny': {
        'name': 'RT-MonoDepth + Swin2-Tiny',
        'description': '平衡版本',
        'expected_fps': '60-75',
        'parameters': '42M',
        'accuracy_improvement': '+20-25%',
        'memory_usage': '<2GB',
        'best_for': '精度和速度平衡'
    },
    'levit_224': {
        'name': 'RT-MonoDepth + LeViT-224',
        'description': '高精度版本',
        'expected_fps': '50-65',
        'parameters': '51M',
        'accuracy_improvement': '+25-30%',
        'memory_usage': '<2.5GB',
        'best_for': '高精度要求，可接受较低帧率'
    }
}


class ModelFactory:
    """模型工厂类"""
    
    @staticmethod
    def create_model(model_type, pretrained_path=None, optimized_for_orin=True, **kwargs):
        """
        创建指定类型的模型
        
        Args:
            model_type (str): 模型类型 ['midas_fusion', 'swin2_tiny', 'levit_224']
            pretrained_path (str): 预训练权重路径
            optimized_for_orin (bool): 是否针对Orin NX Super优化
            **kwargs: 其他模型参数
        
        Returns:
            torch.nn.Module: 创建的模型
        """
        if model_type not in MODEL_CONFIGS:
            raise ValueError(f"Unsupported model type: {model_type}. "
                           f"Supported types: {list(MODEL_CONFIGS.keys())}")
        
        print(f"🚀 创建模型: {MODEL_CONFIGS[model_type]['name']}")
        print(f"📊 预期性能: {MODEL_CONFIGS[model_type]['expected_fps']} FPS")
        print(f"💾 内存使用: {MODEL_CONFIGS[model_type]['memory_usage']}")
        
        if model_type == 'midas_fusion':
            model = create_rtmonodepth_midas_fusion(
                pretrained_path=pretrained_path,
                optimized_for_orin=optimized_for_orin,
                **kwargs
            )
        elif model_type == 'swin2_tiny':
            model = create_rtmonodepth_swin2_tiny(
                pretrained_path=pretrained_path,
                optimized_for_orin=optimized_for_orin,
                **kwargs
            )
        elif model_type == 'levit_224':
            model = create_rtmonodepth_levit_224(
                pretrained_path=pretrained_path,
                optimized_for_orin=optimized_for_orin,
                **kwargs
            )
        
        return model
    
    @staticmethod
    def get_model_info(model_type=None):
        """获取模型信息"""
        if model_type is None:
            return MODEL_CONFIGS
        elif model_type in MODEL_CONFIGS:
            return MODEL_CONFIGS[model_type]
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    @staticmethod
    def list_models():
        """列出所有可用模型"""
        print("📋 可用模型列表:")
        print("=" * 80)
        for model_type, config in MODEL_CONFIGS.items():
            print(f"🔹 {model_type}:")
            print(f"   名称: {config['name']}")
            print(f"   描述: {config['description']}")
            print(f"   预期FPS: {config['expected_fps']} (Orin NX Super)")
            print(f"   参数量: {config['parameters']}")
            print(f"   精度提升: {config['accuracy_improvement']}")
            print(f"   内存使用: {config['memory_usage']}")
            print(f"   适用场景: {config['best_for']}")
            print("-" * 80)
    
    @staticmethod
    def recommend_model(priority='balanced'):
        """
        根据优先级推荐模型
        
        Args:
            priority (str): 优先级 ['speed', 'balanced', 'accuracy']
        
        Returns:
            str: 推荐的模型类型
        """
        recommendations = {
            'speed': 'midas_fusion',
            'balanced': 'swin2_tiny', 
            'accuracy': 'levit_224'
        }
        
        if priority not in recommendations:
            raise ValueError(f"Unknown priority: {priority}. "
                           f"Supported: {list(recommendations.keys())}")
        
        recommended = recommendations[priority]
        config = MODEL_CONFIGS[recommended]
        
        print(f"💡 基于'{priority}'优先级，推荐模型: {recommended}")
        print(f"   {config['name']} - {config['description']}")
        print(f"   预期FPS: {config['expected_fps']}")
        print(f"   适用场景: {config['best_for']}")
        
        return recommended


class ModelBenchmark:
    """模型性能基准测试"""
    
    def __init__(self, device='cuda'):
        self.device = device
    
    def benchmark_model(self, model, input_shape=(1, 3, 192, 640), num_iterations=100):
        """
        对模型进行性能基准测试
        
        Args:
            model: 要测试的模型
            input_shape: 输入张量形状
            num_iterations: 测试迭代次数
        
        Returns:
            dict: 性能指标
        """
        model = model.to(self.device).eval()
        
        # 创建测试输入
        if hasattr(model, 'half') and next(model.parameters()).dtype == torch.float16:
            x = torch.randn(input_shape, dtype=torch.float16).to(self.device)
        else:
            x = torch.randn(input_shape).to(self.device)
        
        # 预热
        print("🔥 模型预热中...")
        with torch.no_grad():
            for _ in range(10):
                _ = model(x)
        
        torch.cuda.synchronize()
        
        # 性能测试
        print("⏱️ 性能测试中...")
        import time
        
        # FPS测试
        start_time = time.time()
        with torch.no_grad():
            for _ in range(num_iterations):
                _ = model(x)
        torch.cuda.synchronize()
        end_time = time.time()
        
        # 计算指标
        total_time = end_time - start_time
        fps = num_iterations / total_time
        avg_latency = total_time / num_iterations * 1000  # ms
        
        # 内存使用
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()
        with torch.no_grad():
            _ = model(x)
        peak_memory = torch.cuda.max_memory_allocated()
        memory_usage = (peak_memory - initial_memory) / 1024**2  # MB
        
        # 参数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        results = {
            'fps': fps,
            'latency_ms': avg_latency,
            'memory_mb': memory_usage,
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / 1024**2  # 假设FP32
        }
        
        return results
    
    def compare_models(self, model_types=['midas_fusion', 'swin2_tiny', 'levit_224']):
        """比较多个模型的性能"""
        print("🏁 开始模型性能对比...")
        print("=" * 100)
        
        results = {}
        for model_type in model_types:
            print(f"\n📊 测试模型: {model_type}")
            print("-" * 50)
            
            try:
                model = ModelFactory.create_model(model_type, optimized_for_orin=True)
                benchmark_results = self.benchmark_model(model)
                results[model_type] = benchmark_results
                
                print(f"✅ {model_type} 测试完成:")
                print(f"   FPS: {benchmark_results['fps']:.1f}")
                print(f"   延迟: {benchmark_results['latency_ms']:.2f} ms")
                print(f"   内存: {benchmark_results['memory_mb']:.1f} MB")
                print(f"   参数量: {benchmark_results['total_params']/1e6:.1f}M")
                
                # 清理GPU内存
                del model
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"❌ {model_type} 测试失败: {e}")
                results[model_type] = None
        
        # 打印对比表格
        print("\n" + "=" * 100)
        print("📈 性能对比总结:")
        print("=" * 100)
        print(f"{'模型':<20} {'FPS':<10} {'延迟(ms)':<12} {'内存(MB)':<12} {'参数量(M)':<12}")
        print("-" * 100)
        
        for model_type, result in results.items():
            if result:
                print(f"{model_type:<20} {result['fps']:<10.1f} {result['latency_ms']:<12.2f} "
                      f"{result['memory_mb']:<12.1f} {result['total_params']/1e6:<12.1f}")
            else:
                print(f"{model_type:<20} {'Failed':<10} {'Failed':<12} {'Failed':<12} {'Failed':<12}")
        
        return results


# 便捷函数
def create_model(model_type='swin2_tiny', **kwargs):
    """便捷的模型创建函数"""
    return ModelFactory.create_model(model_type, **kwargs)


def benchmark_all_models():
    """对所有模型进行基准测试"""
    benchmark = ModelBenchmark()
    return benchmark.compare_models()


if __name__ == "__main__":
    # 示例使用
    print("🎯 RT-MonoDepth模型工厂示例")
    
    # 列出所有模型
    ModelFactory.list_models()
    
    # 获取推荐
    recommended = ModelFactory.recommend_model('balanced')
    
    # 创建推荐模型
    model = ModelFactory.create_model(recommended, optimized_for_orin=True)
    print(f"✅ 成功创建模型: {recommended}")
    
    # 运行基准测试（可选）
    # benchmark_all_models()
