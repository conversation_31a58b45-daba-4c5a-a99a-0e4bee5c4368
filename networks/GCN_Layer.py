import torch
import torch.nn as nn
import torch.nn.functional as F

class SimpleGCNLayer(nn.Module):
    """
    最小实现的 GCN:
    out = ReLU( A * X * W ) 
    假设 A 事先已经是归一化好的邻接矩阵 (例如 D^{-1/2} * A * D^{-1/2})
    这里为了简化, 我们把 A 作为可学习参数或固定都行.
    """
    def __init__(self, in_features, out_features):
        super(SimpleGCNLayer, self).__init__()
        self.linear = nn.Linear(in_features, out_features, bias=False)

    def forward(self, x, A):
        # x: [B, N, C]
        # A: [N, N] or [B, N, N] (如果不同 batch 可以共享一个 A，也可以做成 batch 维度)
        # step1: X * W
        out = self.linear(x)  # [B, N, out_features]
        # step2: A * out
        # 如果 A 不带 batch，则需要 broadcast: out = torch.matmul(A, out) -> [N, out_features]
        # 再加 batch 维度
        out = torch.matmul(A, out)  # [B, N, out_features] 或 [N, out_features]
        out = F.relu(out)
        return out
