# 保守的训练策略：先不使用新模块，确保基础训练正常
python train.py \
--model_name self_small-baseline \
--load_weights_folder /mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/s/ms_640_192 \
--num_epochs 5 \
--learning_rate 1e-4 \
--log_dir ./log/small/stereo \
--data_path /mnt/acer/kitti_jpg \
--num_workers 12 \
--batch_size 8 \
--scales 0 1 2 \

# # 第二阶段：整体微调
# python train.py \
# --model_name self_small-DPTDecoderLite-stage2 \
# --load_weights_folder ./log/small/stereo/self_small-DPTDecoderLite-stage1/models/weights_19 \
# --num_epochs 30 \
# --use_acm \
# --use_s2conv \
# --use_booster \
# --learning_rate 5e-5 \
# --log_dir ./log/small/stereo \
# --data_path /mnt/acer/kitti_jpg \
# --num_workers 12 \
# --batch_size 12 \
# --scales 0 1 2 \
