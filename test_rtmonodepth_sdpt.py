#!/usr/bin/env python3
"""
测试RT-MonoDepth-S + DPT集成模型
验证真实的DPT特征融合是否正常工作
"""

import torch
import time
import sys
import os

def test_rtmonodepth_sdpt():
    """测试RT-MonoDepth-S + DPT模型"""
    print("🧪 测试RT-MonoDepth-S + DPT集成模型...")
    
    try:
        from networks.RTMonoDepth.RTMonodepth_sDPT import RTMonoDepthSDPT, create_rtmonodepth_s_dpt
        print("✅ 成功导入RT-MonoDepth-S + DPT模型")
        
        # 测试不同配置
        configs = [
            {"use_dpt_fusion": False, "name": "原始RT-MonoDepth-S"},
            {"use_dpt_fusion": True, "name": "RT-MonoDepth-S + DPT融合"}
        ]
        
        results = {}
        
        for config in configs:
            print(f"\n🔧 测试配置: {config['name']}")
            print("-" * 50)
            
            # 创建模型
            model = create_rtmonodepth_s_dpt(use_dpt_fusion=config["use_dpt_fusion"])
            model.eval()
            
            # 计算参数量
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            print(f"📊 模型参数:")
            print(f"   总参数: {total_params:,}")
            print(f"   可训练参数: {trainable_params:,}")
            print(f"   模型大小: {total_params * 4 / 1024**2:.1f} MB (FP32)")
            
            # 测试前向传播
            test_input = torch.randn(1, 3, 192, 640)
            
            print(f"🚀 前向传播测试:")
            print(f"   输入形状: {test_input.shape}")
            
            # 性能测试
            warmup_runs = 10
            test_runs = 50
            
            # 预热
            with torch.no_grad():
                for _ in range(warmup_runs):
                    _ = model(test_input)
            
            # 性能测试
            start_time = time.time()
            with torch.no_grad():
                for _ in range(test_runs):
                    outputs = model(test_input)
            end_time = time.time()
            
            avg_time = (end_time - start_time) / test_runs
            fps = 1.0 / avg_time
            
            print(f"   输出数量: {len(outputs)}")
            for key, value in outputs.items():
                print(f"   {key}: {value.shape}")
            
            print(f"⏱️ 性能测试:")
            print(f"   平均延迟: {avg_time*1000:.2f} ms")
            print(f"   FPS: {fps:.1f}")
            
            # 存储结果
            results[config["name"]] = {
                "params": total_params,
                "fps": fps,
                "latency": avg_time * 1000,
                "outputs": outputs
            }
        
        # 对比分析
        print(f"\n" + "=" * 70)
        print(f"📈 对比分析:")
        print(f"=" * 70)
        
        if len(results) == 2:
            original = results["原始RT-MonoDepth-S"]
            dpt_version = results["RT-MonoDepth-S + DPT融合"]
            
            params_increase = (dpt_version["params"] / original["params"] - 1) * 100
            fps_change = (dpt_version["fps"] / original["fps"] - 1) * 100
            latency_change = (dpt_version["latency"] / original["latency"] - 1) * 100
            
            print(f"🔸 参数量变化:")
            print(f"   原始: {original['params']:,}")
            print(f"   DPT版: {dpt_version['params']:,}")
            print(f"   增加: {params_increase:+.1f}% (+{dpt_version['params'] - original['params']:,})")
            
            print(f"\n🔸 性能变化:")
            print(f"   原始FPS: {original['fps']:.1f}")
            print(f"   DPT版FPS: {dpt_version['fps']:.1f}")
            print(f"   FPS变化: {fps_change:+.1f}%")
            
            print(f"\n🔸 延迟变化:")
            print(f"   原始延迟: {original['latency']:.2f} ms")
            print(f"   DPT版延迟: {dpt_version['latency']:.2f} ms")
            print(f"   延迟变化: {latency_change:+.1f}%")
            
            # 输出质量对比
            print(f"\n🔸 输出对比:")
            orig_disp = original["outputs"][("disp", 0)]
            dpt_disp = dpt_version["outputs"][("disp", 0)]

            print(f"   原始深度图形状: {orig_disp.shape}")
            print(f"   DPT深度图形状: {dpt_disp.shape}")
            print(f"   原始深度范围: [{orig_disp.min():.3f}, {orig_disp.max():.3f}]")
            print(f"   DPT深度范围: [{dpt_disp.min():.3f}, {dpt_disp.max():.3f}]")

            # 如果尺寸不同，调整到相同尺寸再比较
            if orig_disp.shape != dpt_disp.shape:
                print(f"   注意: 输出尺寸不同，DPT版本输出更高分辨率")
                # 将DPT输出下采样到原始尺寸进行比较
                dpt_disp_resized = torch.nn.functional.interpolate(
                    dpt_disp, size=orig_disp.shape[2:], mode='bilinear', align_corners=True
                )
                diff = torch.abs(dpt_disp_resized - orig_disp).mean()
                print(f"   调整尺寸后平均差异: {diff:.6f}")
            else:
                diff = torch.abs(dpt_disp - orig_disp).mean()
                print(f"   深度图平均差异: {diff:.6f}")
            
            # 性能评估
            print(f"\n💡 性能评估:")
            if fps_change > -15:
                print(f"   🟢 性能损失很小 ({fps_change:+.1f}%)")
            elif fps_change > -30:
                print(f"   🟡 性能损失适中 ({fps_change:+.1f}%)")
            else:
                print(f"   🔴 性能损失较大 ({fps_change:+.1f}%)")
            
            if params_increase < 50:
                print(f"   🟢 参数增加合理 ({params_increase:+.1f}%)")
            else:
                print(f"   🟡 参数增加较多 ({params_increase:+.1f}%)")
        
        # 验证DPT组件
        print(f"\n🔍 DPT组件验证:")
        dpt_model = create_rtmonodepth_s_dpt(use_dpt_fusion=True)
        
        # 检查DPT特征融合模块
        if hasattr(dpt_model.decoder, 'dpt_fusion'):
            dpt_fusion = dpt_model.decoder.dpt_fusion
            print(f"   ✅ DPT特征融合模块存在")
            print(f"   📊 融合模块参数: {sum(p.numel() for p in dpt_fusion.parameters()):,}")
            
            # 检查关键组件
            components = ['projects', 'refinenet1', 'refinenet2', 'refinenet3', 'refinenet4']
            for comp in components:
                if hasattr(dpt_fusion, comp):
                    print(f"   ✅ {comp} 组件存在")
                else:
                    print(f"   ❌ {comp} 组件缺失")
        else:
            print(f"   ❌ DPT特征融合模块不存在")
        
        print(f"\n🎉 测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dpt_components():
    """单独测试DPT组件"""
    print(f"\n🔧 测试DPT组件...")
    
    try:
        from networks.RTMonoDepth.RTMonodepth_sDPT import (
            ResidualConvUnit, 
            FeatureFusionBlock, 
            DPTFeatureFusion
        )
        
        # 测试残差卷积单元
        print(f"   测试ResidualConvUnit...")
        rcu = ResidualConvUnit(64)
        test_input = torch.randn(1, 64, 32, 32)
        output = rcu(test_input)
        assert output.shape == test_input.shape, "ResidualConvUnit输出形状错误"
        print(f"   ✅ ResidualConvUnit测试通过")
        
        # 测试特征融合块
        print(f"   测试FeatureFusionBlock...")
        ffb = FeatureFusionBlock(64, deconv=True)
        output = ffb(test_input)
        expected_shape = (1, 64, 64, 64)  # 上采样2倍
        assert output.shape == expected_shape, f"FeatureFusionBlock输出形状错误: {output.shape} vs {expected_shape}"
        print(f"   ✅ FeatureFusionBlock测试通过")
        
        # 测试DPT特征融合
        print(f"   测试DPTFeatureFusion...")
        dpt_fusion = DPTFeatureFusion([64, 64, 128, 192], features=256)
        
        # 模拟编码器特征
        features = [
            torch.randn(1, 64, 96, 320),   # 1/2
            torch.randn(1, 64, 48, 160),   # 1/4
            torch.randn(1, 128, 24, 80),   # 1/8
            torch.randn(1, 192, 12, 40),   # 1/16
        ]
        
        output = dpt_fusion(features)
        expected_shape = (1, 32, 96, 320)  # 输出到1/2分辨率
        assert output.shape == expected_shape, f"DPTFeatureFusion输出形状错误: {output.shape} vs {expected_shape}"
        print(f"   ✅ DPTFeatureFusion测试通过")
        
        print(f"✅ 所有DPT组件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ DPT组件测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 RT-MonoDepth-S + DPT集成测试套件")
    print("=" * 70)
    
    # 测试1: 完整模型
    test1_passed = test_rtmonodepth_sdpt()
    
    # 测试2: DPT组件
    test2_passed = test_dpt_components()
    
    # 总结
    print(f"\n" + "=" * 70)
    print(f"🎯 测试结果总结:")
    print(f"   完整模型测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"   DPT组件测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 所有测试通过！RT-MonoDepth-S + DPT集成成功。")
        print(f"💡 这是基于Intel DPT官方实现的真实特征融合策略。")
    else:
        print(f"\n❌ 部分测试失败，请检查实现。")
    
    sys.exit(0 if (test1_passed and test2_passed) else 1)
