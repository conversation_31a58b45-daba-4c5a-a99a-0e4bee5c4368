"""
权重转换和加载工具
用于MiDaS融合模型的权重管理
"""

import torch
import os
import numpy as np
from collections import OrderedDict

class WeightConverter:
    """权重转换器"""
    
    @staticmethod
    def convert_rtmonodepth_to_fusion(rtmonodepth_weights_folder, output_folder):
        """
        将RT-MonoDepth权重转换为MiDaS融合模型格式
        
        Args:
            rtmonodepth_weights_folder: RT-MonoDepth权重文件夹
            output_folder: 输出文件夹
        """
        print(f"🔄 转换RT-MonoDepth权重: {rtmonodepth_weights_folder}")
        
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        
        # 1. 复制编码器权重
        encoder_src = os.path.join(rtmonodepth_weights_folder, "encoder.pth")
        encoder_dst = os.path.join(output_folder, "encoder.pth")
        
        if os.path.exists(encoder_src):
            encoder_weights = torch.load(encoder_src, map_location='cpu')
            # 清理不需要的键
            cleaned_weights = {}
            for k, v in encoder_weights.items():
                if k not in ['height', 'width', 'use_stereo']:
                    cleaned_weights[k] = v
            
            torch.save(cleaned_weights, encoder_dst)
            print(f"✅ 编码器权重已转换: {encoder_dst}")
        
        # 2. 转换解码器权重为融合格式
        depth_src = os.path.join(rtmonodepth_weights_folder, "depth.pth")
        fusion_dst = os.path.join(output_folder, "fusion.pth")
        
        if os.path.exists(depth_src):
            depth_weights = torch.load(depth_src, map_location='cpu')
            
            # 创建融合模型权重结构
            fusion_weights = {}
            
            # 映射解码器权重
            for k, v in depth_weights.items():
                if k.startswith('decoder.'):
                    fusion_weights[k] = v
                else:
                    # 其他权重映射到解码器
                    fusion_weights[f'decoder.{k}'] = v
            
            torch.save(fusion_weights, fusion_dst)
            print(f"✅ 融合权重已转换: {fusion_dst}")
        
        # 3. 复制姿态网络权重
        for model_name in ["pose_encoder", "pose"]:
            src_path = os.path.join(rtmonodepth_weights_folder, f"{model_name}.pth")
            dst_path = os.path.join(output_folder, f"{model_name}.pth")
            
            if os.path.exists(src_path):
                weights = torch.load(src_path, map_location='cpu')
                torch.save(weights, dst_path)
                print(f"✅ {model_name}权重已复制: {dst_path}")
        
        print(f"🎉 权重转换完成: {output_folder}")
    
    @staticmethod
    def load_midas_pretrained_features(midas_weights_path):
        """
        从MiDaS预训练权重中提取特征融合相关的权重
        
        Args:
            midas_weights_path: MiDaS预训练权重路径
            
        Returns:
            dict: 提取的权重字典
        """
        print(f"📥 加载MiDaS预训练权重: {midas_weights_path}")
        
        if not os.path.exists(midas_weights_path):
            print(f"⚠️ MiDaS权重文件不存在: {midas_weights_path}")
            return {}
        
        try:
            midas_weights = torch.load(midas_weights_path, map_location='cpu')
            
            # 提取特征融合相关的权重
            fusion_weights = {}
            
            # 根据MiDaS的权重结构提取相关权重
            # 这里需要根据具体的MiDaS模型结构进行调整
            for k, v in midas_weights.items():
                if any(keyword in k.lower() for keyword in ['fusion', 'reassemble', 'refinement']):
                    fusion_weights[k] = v
            
            print(f"✅ 提取了 {len(fusion_weights)} 个MiDaS特征融合权重")
            return fusion_weights
            
        except Exception as e:
            print(f"❌ 加载MiDaS权重失败: {e}")
            return {}
    
    @staticmethod
    def initialize_fusion_weights(fusion_model, rtmonodepth_weights=None, midas_weights=None):
        """
        初始化融合模型权重
        
        Args:
            fusion_model: MiDaS融合模型
            rtmonodepth_weights: RT-MonoDepth权重路径
            midas_weights: MiDaS权重路径
        """
        print("🔧 初始化融合模型权重...")
        
        # 1. 加载RT-MonoDepth编码器权重
        if rtmonodepth_weights:
            encoder_path = os.path.join(rtmonodepth_weights, "encoder.pth")
            if os.path.exists(encoder_path):
                print("📥 加载RT-MonoDepth编码器权重...")
                encoder_dict = torch.load(encoder_path, map_location='cpu')
                
                # 清理权重
                cleaned_dict = {}
                for k, v in encoder_dict.items():
                    if k not in ['height', 'width', 'use_stereo']:
                        cleaned_dict[k] = v
                
                # 加载到编码器
                model_dict = fusion_model.encoder.state_dict()
                pretrained_dict = {k: v for k, v in cleaned_dict.items() if k in model_dict}
                model_dict.update(pretrained_dict)
                fusion_model.encoder.load_state_dict(model_dict)
                print(f"✅ 加载了 {len(pretrained_dict)} 个编码器参数")
        
        # 2. 初始化解码器权重
        if rtmonodepth_weights:
            depth_path = os.path.join(rtmonodepth_weights, "depth.pth")
            if os.path.exists(depth_path):
                print("📥 初始化解码器权重...")
                depth_dict = torch.load(depth_path, map_location='cpu')
                
                # 尝试映射到增强解码器
                model_dict = fusion_model.decoder.state_dict()
                pretrained_dict = {}
                
                for k, v in depth_dict.items():
                    if k in model_dict and v.shape == model_dict[k].shape:
                        pretrained_dict[k] = v
                
                if pretrained_dict:
                    model_dict.update(pretrained_dict)
                    fusion_model.decoder.load_state_dict(model_dict, strict=False)
                    print(f"✅ 初始化了 {len(pretrained_dict)} 个解码器参数")
        
        # 3. 初始化MiDaS融合模块（如果有MiDaS权重）
        if midas_weights and hasattr(fusion_model, 'midas_fusion'):
            midas_features = WeightConverter.load_midas_pretrained_features(midas_weights)
            if midas_features:
                print("📥 初始化MiDaS融合模块...")
                # 这里需要根据具体的权重映射策略进行实现
                # 由于MiDaS和我们的融合模块结构可能不完全一致，需要手动映射
                print("⚠️ MiDaS权重映射需要根据具体模型结构实现")
        
        print("🎉 权重初始化完成！")
    
    @staticmethod
    def verify_weights(weights_folder):
        """
        验证权重文件的完整性
        
        Args:
            weights_folder: 权重文件夹路径
        """
        print(f"🔍 验证权重文件: {weights_folder}")
        
        required_files = ["encoder.pth", "fusion.pth"]
        optional_files = ["pose_encoder.pth", "pose.pth", "adam.pth"]
        
        missing_files = []
        existing_files = []
        
        # 检查必需文件
        for file_name in required_files:
            file_path = os.path.join(weights_folder, file_name)
            if os.path.exists(file_path):
                existing_files.append(file_name)
                # 检查文件大小
                size_mb = os.path.getsize(file_path) / 1024 / 1024
                print(f"✅ {file_name}: {size_mb:.1f} MB")
            else:
                missing_files.append(file_name)
                print(f"❌ {file_name}: 缺失")
        
        # 检查可选文件
        for file_name in optional_files:
            file_path = os.path.join(weights_folder, file_name)
            if os.path.exists(file_path):
                existing_files.append(file_name)
                size_mb = os.path.getsize(file_path) / 1024 / 1024
                print(f"📁 {file_name}: {size_mb:.1f} MB")
        
        # 验证权重内容
        for file_name in existing_files:
            if file_name.endswith('.pth'):
                file_path = os.path.join(weights_folder, file_name)
                try:
                    weights = torch.load(file_path, map_location='cpu')
                    param_count = sum(v.numel() for v in weights.values() if isinstance(v, torch.Tensor))
                    print(f"📊 {file_name}: {param_count:,} 参数")
                except Exception as e:
                    print(f"⚠️ {file_name}: 加载失败 - {e}")
        
        if missing_files:
            print(f"⚠️ 缺失必需文件: {missing_files}")
            return False
        else:
            print("✅ 权重文件验证通过！")
            return True


def convert_weights_example():
    """权重转换示例"""
    # RT-MonoDepth权重路径
    rtmonodepth_weights = "/mnt/acer/RT-MonoDepth-main/weights/RTMonoDepth/s/ms_640_192"
    
    # 输出路径
    output_folder = "./weights/midas_fusion_init"
    
    # 转换权重
    WeightConverter.convert_rtmonodepth_to_fusion(rtmonodepth_weights, output_folder)
    
    # 验证权重
    WeightConverter.verify_weights(output_folder)


if __name__ == "__main__":
    convert_weights_example()
