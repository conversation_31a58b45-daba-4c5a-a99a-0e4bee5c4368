import os
import glob
import torch
import numpy as np
import cv2
from PIL import Image

# 导入两个模型
from networks.RTMonoDepth.self.small.RTMonodepth_final_s_1 import RTMonoDepthFusion, DepthEncoder as NewEncoder
from networks.RTMonoDepth.RTMonoDepth_s import DepthEncoder as OldEncoder, DepthDecoder

def load_image(image_path, height=192, width=640):
    """加载并预处理图像"""
    image = Image.open(image_path).convert('RGB')
    image = image.resize((width, height), Image.LANCZOS)
    image = np.array(image).astype(np.float32) / 255.0
    image = torch.from_numpy(image.transpose(2, 0, 1)).unsqueeze(0)
    return image

def compare_single_image(image_path, new_model, old_encoder, old_decoder, device):
    """比较单张图像的两个模型输出"""
    try:
        # 加载图像
        image = load_image(image_path).to(device)
        
        with torch.no_grad():
            # 新模型推理
            new_outputs = new_model(image)
            new_depth = new_outputs[("disp", 0)]
            
            # 旧模型推理
            old_features = old_encoder(image)
            old_outputs = old_decoder(old_features)
            old_depth = old_outputs[("disp", 0)]
            
            # 转换为numpy数组
            new_depth_np = new_depth.squeeze().cpu().numpy()
            old_depth_np = old_depth.squeeze().cpu().numpy()
            
            # 如果尺寸不同，将新模型的输出下采样
            if new_depth_np.shape != old_depth_np.shape:
                new_depth_resized = cv2.resize(new_depth_np, (old_depth_np.shape[1], old_depth_np.shape[0]), 
                                             interpolation=cv2.INTER_LINEAR)
            else:
                new_depth_resized = new_depth_np
            
            # 计算统计指标
            diff = np.abs(new_depth_resized - old_depth_np)
            relative_diff = diff / (np.abs(old_depth_np) + 1e-8)
            correlation = np.corrcoef(new_depth_resized.flatten(), old_depth_np.flatten())[0, 1]
            
            # 计算深度值的有效性（避免异常值）
            new_valid = np.isfinite(new_depth_resized)
            old_valid = np.isfinite(old_depth_np)
            valid_mask = new_valid & old_valid
            
            if np.sum(valid_mask) == 0:
                return None
            
            stats = {
                'image_path': image_path,
                'new_shape': new_depth_np.shape,
                'old_shape': old_depth_np.shape,
                'new_mean': np.mean(new_depth_resized[valid_mask]),
                'new_std': np.std(new_depth_resized[valid_mask]),
                'old_mean': np.mean(old_depth_np[valid_mask]),
                'old_std': np.std(old_depth_np[valid_mask]),
                'mean_abs_diff': np.mean(diff[valid_mask]),
                'max_diff': np.max(diff[valid_mask]),
                'mean_rel_diff': np.mean(relative_diff[valid_mask]),
                'correlation': correlation if np.isfinite(correlation) else 0.0,
                'valid_pixels': np.sum(valid_mask),
                'total_pixels': valid_mask.size
            }
            
            return stats
            
    except Exception as e:
        print(f"处理图像 {image_path} 时出错: {str(e)}")
        return None

def batch_test():
    """批量测试多张图像"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 初始化模型
    print("初始化模型...")
    
    # 新模型
    new_encoder = NewEncoder().to(device).eval()
    new_model = RTMonoDepthFusion(
        new_encoder,
        use_booster=True,
        use_acm=True,
        use_s2conv=True
    ).to(device).eval()
    
    # 旧模型
    old_encoder = OldEncoder().to(device).eval()
    old_decoder = DepthDecoder(old_encoder.num_ch_enc).to(device).eval()
    
    # 获取测试图像列表
    image_pattern = "fortest/data/*.jpg"
    image_paths = glob.glob(image_pattern)
    print(f"找到 {len(image_paths)} 张测试图像")
    
    # 限制测试图像数量以节省时间
    max_images = 20
    if len(image_paths) > max_images:
        image_paths = image_paths[:max_images]
        print(f"限制测试图像数量为 {max_images} 张")
    
    # 批量处理
    all_stats = []
    for i, image_path in enumerate(image_paths):
        print(f"处理图像 {i+1}/{len(image_paths)}: {os.path.basename(image_path)}")
        stats = compare_single_image(image_path, new_model, old_encoder, old_decoder, device)
        if stats is not None:
            all_stats.append(stats)
    
    if not all_stats:
        print("没有成功处理的图像")
        return
    
    # 计算总体统计
    print(f"\n成功处理 {len(all_stats)} 张图像")
    print("="*60)
    
    # 输出尺寸信息
    new_shapes = [stats['new_shape'] for stats in all_stats]
    old_shapes = [stats['old_shape'] for stats in all_stats]
    print(f"新模型输出尺寸: {new_shapes[0]} (所有图像)")
    print(f"旧模型输出尺寸: {old_shapes[0]} (所有图像)")
    
    # 计算平均统计
    metrics = ['new_mean', 'new_std', 'old_mean', 'old_std', 
               'mean_abs_diff', 'max_diff', 'mean_rel_diff', 'correlation']
    
    print("\n平均统计指标:")
    print("-"*40)
    for metric in metrics:
        values = [stats[metric] for stats in all_stats if np.isfinite(stats[metric])]
        if values:
            avg_val = np.mean(values)
            std_val = np.std(values)
            print(f"{metric:15s}: {avg_val:8.4f} ± {std_val:6.4f}")
    
    # 分析相关性分布
    correlations = [stats['correlation'] for stats in all_stats if np.isfinite(stats['correlation'])]
    if correlations:
        print(f"\n相关性分析:")
        print(f"平均相关性: {np.mean(correlations):.4f}")
        print(f"相关性标准差: {np.std(correlations):.4f}")
        print(f"最高相关性: {np.max(correlations):.4f}")
        print(f"最低相关性: {np.min(correlations):.4f}")
        
        # 相关性分布
        high_corr = sum(1 for c in correlations if c > 0.5)
        med_corr = sum(1 for c in correlations if 0.2 <= c <= 0.5)
        low_corr = sum(1 for c in correlations if c < 0.2)
        
        print(f"\n相关性分布:")
        print(f"高相关性 (>0.5):  {high_corr:2d} 张 ({high_corr/len(correlations)*100:.1f}%)")
        print(f"中等相关性 (0.2-0.5): {med_corr:2d} 张 ({med_corr/len(correlations)*100:.1f}%)")
        print(f"低相关性 (<0.2): {low_corr:2d} 张 ({low_corr/len(correlations)*100:.1f}%)")
    
    # 深度值范围分析
    new_means = [stats['new_mean'] for stats in all_stats]
    old_means = [stats['old_mean'] for stats in all_stats]
    
    print(f"\n深度值范围分析:")
    print(f"新模型深度范围: [{np.min(new_means):.4f}, {np.max(new_means):.4f}]")
    print(f"旧模型深度范围: [{np.min(old_means):.4f}, {np.max(old_means):.4f}]")
    
    # 差异分析
    abs_diffs = [stats['mean_abs_diff'] for stats in all_stats]
    rel_diffs = [stats['mean_rel_diff'] for stats in all_stats]
    
    print(f"\n差异分析:")
    print(f"平均绝对差异: {np.mean(abs_diffs):.4f} ± {np.std(abs_diffs):.4f}")
    print(f"平均相对差异: {np.mean(rel_diffs):.4f} ± {np.std(rel_diffs):.4f}")
    
    # 结论
    print("\n"+"="*60)
    print("结论分析:")
    
    avg_correlation = np.mean(correlations) if correlations else 0
    avg_abs_diff = np.mean(abs_diffs)
    avg_rel_diff = np.mean(rel_diffs)
    
    if avg_correlation > 0.3:
        print("✓ 两个模型的输出具有一定的相关性")
    else:
        print("✗ 两个模型的输出相关性较低")
    
    if avg_rel_diff < 0.5:
        print("✓ 相对差异在可接受范围内")
    else:
        print("✗ 相对差异较大，可能存在显著不同")
    
    print(f"\n新模型的主要特点:")
    print(f"- 输出分辨率提高了 4 倍 ({new_shapes[0]} vs {old_shapes[0]})")
    print(f"- 平均相关性: {avg_correlation:.3f}")
    print(f"- 平均相对差异: {avg_rel_diff:.3f}")

if __name__ == '__main__':
    batch_test()
